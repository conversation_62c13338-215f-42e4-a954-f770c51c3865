"""
tar.gz压缩管理器
提供文件和目录的压缩、解压缩功能
"""
import os
import tarfile
import tempfile
import shutil
from typing import Optional, Callable, Tuple, List
import threading
import time


class TarCompressionManager:
    """tar.gz压缩管理器"""
    
    def __init__(self, compression_level: int = 6):
        """
        初始化压缩管理器
        
        Args:
            compression_level: 压缩级别 (0-9, 0=无压缩, 9=最大压缩)
        """
        self.compression_level = compression_level
        self.cancel_operation = False
        self.temp_dir = None
        
    def set_temp_directory(self, temp_dir: str):
        """设置临时目录"""
        self.temp_dir = temp_dir
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
    
    def get_temp_directory(self) -> str:
        """获取临时目录"""
        if self.temp_dir is None:
            self.temp_dir = tempfile.mkdtemp(prefix="backup_tool_")
        return self.temp_dir
    
    def compress_directory(self, source_path: str, archive_path: str = None, 
                          progress_callback: Optional[Callable] = None,
                          exclude_patterns: List[str] = None) -> Tuple[bool, str, str]:
        """
        压缩目录为tar.gz文件
        
        Args:
            source_path: 源目录路径
            archive_path: 目标压缩文件路径（可选，自动生成）
            progress_callback: 进度回调函数 (current, total, filename)
            exclude_patterns: 排除的文件模式列表
            
        Returns:
            (成功标志, 消息, 压缩文件路径)
        """
        try:
            self.cancel_operation = False
            
            if not os.path.exists(source_path):
                return False, f"源路径不存在: {source_path}", ""
            
            # 生成压缩文件路径
            if archive_path is None:
                temp_dir = self.get_temp_directory()
                basename = os.path.basename(source_path.rstrip('/\\'))
                timestamp = int(time.time())
                archive_path = os.path.join(temp_dir, f"{basename}_{timestamp}.tar.gz")
            
            # 确保目标目录存在
            archive_dir = os.path.dirname(archive_path)
            if not os.path.exists(archive_dir):
                os.makedirs(archive_dir)
            
            # 统计文件数量
            total_files = self._count_files(source_path, exclude_patterns)
            processed_files = 0
            
            # 创建tar.gz文件
            with tarfile.open(archive_path, f'w:gz', compresslevel=self.compression_level) as tar:
                
                def add_file_with_progress(file_path: str, arcname: str):
                    nonlocal processed_files
                    
                    if self.cancel_operation:
                        raise Exception("压缩操作已取消")
                    
                    tar.add(file_path, arcname=arcname)
                    processed_files += 1
                    
                    if progress_callback:
                        progress_callback(processed_files, total_files, os.path.basename(file_path))
                
                # 添加文件到压缩包
                if os.path.isfile(source_path):
                    # 单个文件
                    add_file_with_progress(source_path, os.path.basename(source_path))
                else:
                    # 目录
                    for root, dirs, files in os.walk(source_path):
                        # 检查是否取消
                        if self.cancel_operation:
                            raise Exception("压缩操作已取消")
                        
                        # 过滤排除的目录
                        if exclude_patterns:
                            dirs[:] = [d for d in dirs if not self._should_exclude(d, exclude_patterns)]
                        
                        for file in files:
                            if self.cancel_operation:
                                raise Exception("压缩操作已取消")
                            
                            file_path = os.path.join(root, file)
                            
                            # 检查是否排除此文件
                            if exclude_patterns and self._should_exclude(file, exclude_patterns):
                                continue
                            
                            # 计算相对路径
                            rel_path = os.path.relpath(file_path, source_path)
                            add_file_with_progress(file_path, rel_path)
            
            # 获取压缩文件信息
            archive_size = os.path.getsize(archive_path)
            original_size = self._get_directory_size(source_path)
            compression_ratio = (1 - archive_size / original_size) * 100 if original_size > 0 else 0
            
            message = f"压缩完成。原始大小: {self._format_size(original_size)}, " \
                     f"压缩后: {self._format_size(archive_size)}, " \
                     f"压缩率: {compression_ratio:.1f}%"
            
            return True, message, archive_path
            
        except Exception as e:
            if "已取消" in str(e):
                # 清理未完成的压缩文件
                if archive_path and os.path.exists(archive_path):
                    try:
                        os.remove(archive_path)
                    except:
                        pass
                return False, "压缩操作已取消", ""
            return False, f"压缩失败: {str(e)}", ""
    
    def decompress_archive(self, archive_path: str, target_path: str,
                          progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        解压tar.gz文件
        
        Args:
            archive_path: 压缩文件路径
            target_path: 目标解压目录
            progress_callback: 进度回调函数 (current, total, filename)
            
        Returns:
            (成功标志, 消息)
        """
        try:
            self.cancel_operation = False
            
            if not os.path.exists(archive_path):
                return False, f"压缩文件不存在: {archive_path}"
            
            if not tarfile.is_tarfile(archive_path):
                return False, f"不是有效的tar文件: {archive_path}"
            
            # 确保目标目录存在
            if not os.path.exists(target_path):
                os.makedirs(target_path)
            
            with tarfile.open(archive_path, 'r:gz') as tar:
                members = tar.getmembers()
                total_files = len(members)
                processed_files = 0
                
                for member in members:
                    if self.cancel_operation:
                        raise Exception("解压操作已取消")
                    
                    # 安全检查：防止路径遍历攻击
                    if not self._is_safe_path(target_path, member.name):
                        continue
                    
                    tar.extract(member, target_path)
                    processed_files += 1
                    
                    if progress_callback:
                        progress_callback(processed_files, total_files, member.name)
            
            return True, f"解压完成，共解压 {total_files} 个文件"
            
        except Exception as e:
            if "已取消" in str(e):
                return False, "解压操作已取消"
            return False, f"解压失败: {str(e)}"
    
    def compress_remote_directory(self, ssh_client, remote_path: str, 
                                 progress_callback: Optional[Callable] = None) -> Tuple[bool, str, str]:
        """
        在远程服务器上压缩目录
        
        Args:
            ssh_client: SSH客户端实例
            remote_path: 远程目录路径
            progress_callback: 进度回调函数
            
        Returns:
            (成功标志, 消息, 远程压缩文件路径)
        """
        try:
            if not ssh_client.connected:
                return False, "SSH未连接", ""
            
            # 生成远程临时文件名
            basename = os.path.basename(remote_path.rstrip('/'))
            timestamp = int(time.time())
            remote_archive = f"/tmp/{basename}_{timestamp}.tar.gz"
            
            # 构建tar命令
            tar_cmd = f"cd '{os.path.dirname(remote_path)}' && tar -czf '{remote_archive}' '{os.path.basename(remote_path)}'"
            
            # 执行压缩命令
            stdin, stdout, stderr = ssh_client.ssh.exec_command(tar_cmd)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status == 0:
                # 获取压缩文件大小
                size_cmd = f"ls -l '{remote_archive}' | awk '{{print $5}}'"
                stdin, stdout, stderr = ssh_client.ssh.exec_command(size_cmd)
                size_output = stdout.read().decode().strip()
                
                try:
                    file_size = int(size_output)
                    message = f"远程压缩完成，文件大小: {self._format_size(file_size)}"
                except:
                    message = "远程压缩完成"
                
                return True, message, remote_archive
            else:
                error_msg = stderr.read().decode().strip()
                return False, f"远程压缩失败: {error_msg}", ""
                
        except Exception as e:
            return False, f"远程压缩异常: {str(e)}", ""
    
    def decompress_remote_archive(self, ssh_client, remote_archive: str, remote_target: str,
                                 progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        在远程服务器上解压文件
        
        Args:
            ssh_client: SSH客户端实例
            remote_archive: 远程压缩文件路径
            remote_target: 远程目标目录
            progress_callback: 进度回调函数
            
        Returns:
            (成功标志, 消息)
        """
        try:
            if not ssh_client.connected:
                return False, "SSH未连接"
            
            # 确保目标目录存在
            mkdir_cmd = f"mkdir -p '{remote_target}'"
            ssh_client.ssh.exec_command(mkdir_cmd)
            
            # 构建解压命令
            tar_cmd = f"cd '{remote_target}' && tar -xzf '{remote_archive}'"
            
            # 执行解压命令
            stdin, stdout, stderr = ssh_client.ssh.exec_command(tar_cmd)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status == 0:
                return True, "远程解压完成"
            else:
                error_msg = stderr.read().decode().strip()
                return False, f"远程解压失败: {error_msg}"
                
        except Exception as e:
            return False, f"远程解压异常: {str(e)}"
    
    def cleanup_remote_file(self, ssh_client, remote_file: str) -> bool:
        """清理远程临时文件"""
        try:
            if ssh_client.connected:
                rm_cmd = f"rm -f '{remote_file}'"
                ssh_client.ssh.exec_command(rm_cmd)
                return True
        except:
            pass
        return False
    
    def cancel_current_operation(self):
        """取消当前操作"""
        self.cancel_operation = True
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None
            except Exception as e:
                print(f"清理临时文件失败: {e}")
    
    def _count_files(self, path: str, exclude_patterns: List[str] = None) -> int:
        """统计文件数量"""
        if os.path.isfile(path):
            return 1
        
        count = 0
        try:
            for root, dirs, files in os.walk(path):
                # 过滤排除的目录
                if exclude_patterns:
                    dirs[:] = [d for d in dirs if not self._should_exclude(d, exclude_patterns)]
                
                for file in files:
                    if not exclude_patterns or not self._should_exclude(file, exclude_patterns):
                        count += 1
        except:
            pass
        return count
    
    def _should_exclude(self, name: str, patterns: List[str]) -> bool:
        """检查是否应该排除文件/目录"""
        import fnmatch
        for pattern in patterns:
            if fnmatch.fnmatch(name, pattern):
                return True
        return False
    
    def _is_safe_path(self, base_path: str, member_path: str) -> bool:
        """检查解压路径是否安全（防止路径遍历攻击）"""
        full_path = os.path.join(base_path, member_path)
        return os.path.commonpath([base_path, full_path]) == base_path
    
    def _get_directory_size(self, path: str) -> int:
        """获取目录总大小"""
        total_size = 0
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            
            for root, dirs, files in os.walk(path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except:
                        pass
        except:
            pass
        return total_size
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
