#!/usr/bin/env python3
"""
模块测试脚本
用于验证各个模块是否正常工作
"""
import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_config_manager():
    """测试配置管理器"""
    print("测试配置管理器...")
    try:
        from config_manager import ConfigManager
        
        config = ConfigManager("test_servers.json")
        
        # 测试添加服务器
        success = config.add_server("test_server", "*************", 22, "testuser", "testpass")
        print(f"添加服务器: {'成功' if success else '失败'}")
        
        # 测试获取服务器
        server = config.get_server("test_server")
        print(f"获取服务器: {'成功' if server else '失败'}")
        if server:
            print(f"  主机: {server['host']}")
            print(f"  端口: {server['port']}")
            print(f"  用户名: {server['username']}")
        
        # 测试列出服务器
        servers = config.list_servers()
        print(f"服务器列表: {servers}")
        
        # 清理测试文件
        if os.path.exists("test_servers.json"):
            os.remove("test_servers.json")
        if os.path.exists("key.key"):
            os.remove("key.key")
        
        print("配置管理器测试完成\n")
        return True
        
    except Exception as e:
        print(f"配置管理器测试失败: {e}")
        return False

def test_ssh_client():
    """测试SSH客户端"""
    print("测试SSH客户端...")
    try:
        from ssh_client import SSHClient
        
        client = SSHClient()
        print("SSH客户端创建成功")
        
        # 测试连接状态
        print(f"连接状态: {client.connected}")
        
        # 测试断开连接
        client.disconnect()
        print("断开连接测试完成")
        
        print("SSH客户端测试完成\n")
        return True
        
    except Exception as e:
        print(f"SSH客户端测试失败: {e}")
        return False

def test_backup_manager():
    """测试备份管理器"""
    print("测试备份管理器...")
    try:
        from backup_manager import BackupManager
        
        manager = BackupManager()
        print("备份管理器创建成功")
        
        # 测试连接状态
        status = manager.get_connection_status()
        print(f"连接状态: {status}")
        
        # 测试备份历史
        history = manager.get_backup_history()
        print(f"备份历史记录数: {len(history)}")
        
        print("备份管理器测试完成\n")
        return True
        
    except Exception as e:
        print(f"备份管理器测试失败: {e}")
        return False

def test_gui_imports():
    """测试GUI模块导入"""
    print("测试GUI模块导入...")
    try:
        import tkinter as tk
        print("tkinter导入成功")
        
        from gui import BackupToolGUI
        print("GUI模块导入成功")
        
        print("GUI模块测试完成\n")
        return True
        
    except Exception as e:
        print(f"GUI模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 模块测试开始 ===\n")
    
    tests = [
        ("配置管理器", test_config_manager),
        ("SSH客户端", test_ssh_client),
        ("备份管理器", test_backup_manager),
        ("GUI模块", test_gui_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("=== 测试结果 ===")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("所有测试通过！程序应该可以正常运行。")
    else:
        print("部分测试失败，请检查相关模块。")

if __name__ == "__main__":
    main()
