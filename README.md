# 远程文件备份工具

一个基于Python和Tkinter的图形化远程文件备份工具，支持通过SSH连接到Linux服务器进行文件备份和恢复操作。

## 功能特性

- 🔐 **安全连接**: 使用SSH协议连接远程服务器，密码加密存储
- 📁 **双向传输**: 支持本地到远程和远程到本地的文件夹备份
- 🖥️ **图形界面**: 基于Tkinter的友好用户界面
- 📊 **进度显示**: 实时显示传输进度和状态
- 🔧 **多服务器**: 支持管理多个SSH服务器配置
- 📝 **操作日志**: 详细的操作日志和历史记录
- 📦 **独立打包**: 可打包成独立的可执行文件

## 系统要求

- Python 3.7+
- Windows 10/11 (主要测试平台)
- 网络连接到目标Linux服务器

## 安装和运行

### 方法1: 源码运行

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd directory-transfer
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   ```

3. **激活虚拟环境**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

4. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

5. **运行程序**
   ```bash
   python main.py
   ```

### 方法2: 打包成可执行文件

1. **确保已安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行打包脚本**
   ```bash
   # Windows
   build.bat
   
   # 或者直接运行Python脚本
   python build.py
   ```

3. **运行可执行文件**
   ```bash
   dist\RemoteBackupTool.exe
   ```

## 使用说明

### 1. 添加服务器配置

首次使用时，需要添加SSH服务器配置：

1. 点击"添加服务器"按钮
2. 填写服务器信息：
   - 服务器名称：自定义名称
   - 主机地址：服务器IP或域名
   - 端口：SSH端口（默认22）
   - 用户名：SSH用户名
   - 密码：SSH密码
3. 点击"确定"保存配置

### 2. 连接服务器

1. 在下拉列表中选择服务器
2. 点击"测试连接"验证连接
3. 连接成功后状态显示为"已连接"

### 3. 执行备份操作

#### 下载备份（远程→本地）
1. 选择传输模式为"下载"
2. 设置本地路径（点击"浏览"选择目录）
3. 设置远程路径（输入远程目录路径）
4. 点击"开始传输"

#### 上传恢复（本地→远程）
1. 选择传输模式为"上传"
2. 设置本地路径（点击"浏览"选择目录）
3. 设置远程路径（输入远程目录路径）
4. 点击"开始传输"

### 4. 监控进度

- 进度条显示传输进度
- 操作日志显示详细信息
- 可以随时点击"取消传输"中止操作

## 文件说明

### 程序文件
- `main.py`: 主程序入口
- `src/`: 源代码目录
  - `gui.py`: 图形界面模块
  - `ssh_client.py`: SSH客户端模块
  - `config_manager.py`: 配置管理模块
  - `backup_manager.py`: 备份管理模块
- `requirements.txt`: Python依赖包列表
- `build.py`: 打包脚本
- `test_modules.py`: 模块测试脚本

### 配置文件（运行时生成）
- `servers.json`: 服务器配置文件（加密存储）
- `key.key`: 加密密钥文件
- `backup_history.json`: 备份历史记录

## 安全说明

- 服务器密码使用AES加密存储
- 加密密钥存储在本地`key.key`文件中
- 建议定期备份配置文件
- 不要将密钥文件分享给他人

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证服务器地址和端口
   - 确认用户名和密码正确
   - 检查SSH服务是否启用

2. **传输中断**
   - 检查网络稳定性
   - 确认磁盘空间充足
   - 检查文件权限

3. **程序无法启动**
   - 确认Python版本兼容
   - 检查依赖包是否正确安装
   - 运行`python test_modules.py`检查模块

### 日志查看

程序运行时的详细日志会显示在界面的"操作日志"区域，包括：
- 连接状态
- 传输进度
- 错误信息
- 操作结果

## 技术架构

- **GUI框架**: Tkinter
- **SSH客户端**: Paramiko
- **加密**: Cryptography
- **打包工具**: PyInstaller

## 开发和贡献

### 开发环境设置

1. 安装开发依赖
2. 运行测试：`python test_modules.py`
3. 代码格式化和检查

### 项目结构
```
directory-transfer/
├── src/                    # 源代码
│   ├── __init__.py
│   ├── gui.py             # 图形界面
│   ├── ssh_client.py      # SSH客户端
│   ├── config_manager.py  # 配置管理
│   └── backup_manager.py  # 备份管理
├── main.py                # 主程序
├── requirements.txt       # 依赖列表
├── build.py              # 打包脚本
├── build.bat             # Windows打包批处理
├── test_modules.py       # 测试脚本
└── README.md             # 说明文档
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 技术支持

---

**注意**: 请确保遵守相关法律法规，仅在授权的服务器上使用此工具。
