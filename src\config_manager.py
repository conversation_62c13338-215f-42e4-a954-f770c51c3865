"""
SSH配置管理模块
用于管理多个SSH服务器的连接信息
"""
import json
import os
from typing import Dict, List, Optional
from cryptography.fernet import Fernet
import base64


class ConfigManager:
    """SSH配置管理器"""
    
    def __init__(self, config_file: str = "servers.json"):
        self.config_file = config_file
        self.key_file = "key.key"
        self._ensure_key_exists()
        self.cipher = Fernet(self._load_key())
    
    def _ensure_key_exists(self):
        """确保加密密钥文件存在"""
        if not os.path.exists(self.key_file):
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
    
    def _load_key(self) -> bytes:
        """加载加密密钥"""
        with open(self.key_file, 'rb') as f:
            return f.read()
    
    def _encrypt_password(self, password: str) -> str:
        """加密密码"""
        encrypted = self.cipher.encrypt(password.encode())
        return base64.b64encode(encrypted).decode()
    
    def _decrypt_password(self, encrypted_password: str) -> str:
        """解密密码"""
        encrypted_bytes = base64.b64decode(encrypted_password.encode())
        decrypted = self.cipher.decrypt(encrypted_bytes)
        return decrypted.decode()
    
    def add_server(self, name: str, host: str, port: int, username: str, password: str) -> bool:
        """添加服务器配置"""
        try:
            servers = self.load_servers()
            
            # 检查服务器名称是否已存在
            if name in servers:
                return False
            
            servers[name] = {
                "host": host,
                "port": port,
                "username": username,
                "password": self._encrypt_password(password)
            }
            
            self.save_servers(servers)
            return True
        except Exception as e:
            print(f"添加服务器配置失败: {e}")
            return False
    
    def remove_server(self, name: str) -> bool:
        """删除服务器配置"""
        try:
            servers = self.load_servers()
            if name in servers:
                del servers[name]
                self.save_servers(servers)
                return True
            return False
        except Exception as e:
            print(f"删除服务器配置失败: {e}")
            return False
    
    def update_server(self, name: str, host: str = None, port: int = None, 
                     username: str = None, password: str = None) -> bool:
        """更新服务器配置"""
        try:
            servers = self.load_servers()
            if name not in servers:
                return False
            
            if host is not None:
                servers[name]["host"] = host
            if port is not None:
                servers[name]["port"] = port
            if username is not None:
                servers[name]["username"] = username
            if password is not None:
                servers[name]["password"] = self._encrypt_password(password)
            
            self.save_servers(servers)
            return True
        except Exception as e:
            print(f"更新服务器配置失败: {e}")
            return False
    
    def get_server(self, name: str) -> Optional[Dict]:
        """获取服务器配置"""
        try:
            servers = self.load_servers()
            if name in servers:
                server_config = servers[name].copy()
                server_config["password"] = self._decrypt_password(server_config["password"])
                return server_config
            return None
        except Exception as e:
            print(f"获取服务器配置失败: {e}")
            return None
    
    def list_servers(self) -> List[str]:
        """获取所有服务器名称列表"""
        try:
            servers = self.load_servers()
            return list(servers.keys())
        except Exception as e:
            print(f"获取服务器列表失败: {e}")
            return []
    
    def load_servers(self) -> Dict:
        """从文件加载服务器配置"""
        if not os.path.exists(self.config_file):
            return {}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def save_servers(self, servers: Dict) -> bool:
        """保存服务器配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(servers, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False


# 示例配置文件格式
EXAMPLE_CONFIG = {
    "server1": {
        "host": "*************",
        "port": 22,
        "username": "user1",
        "password": "encrypted_password_here"
    },
    "server2": {
        "host": "example.com",
        "port": 2222,
        "username": "user2",
        "password": "encrypted_password_here"
    }
}
