#!/usr/bin/env python3
"""
验证更新脚本
确保所有新功能都正常工作
"""
import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)


def verify_remote_directory_dialog():
    """验证远程目录浏览对话框"""
    print("验证远程目录浏览功能...")
    
    try:
        from gui import RemoteDirectoryDialog
        print("✓ RemoteDirectoryDialog 类导入成功")
        
        # 检查类的方法
        required_methods = [
            'create_dialog_widgets',
            'load_directory', 
            'on_item_double_click',
            'go_to_path',
            'go_up',
            'refresh',
            'select_current_dir',
            'format_size'
        ]
        
        for method in required_methods:
            if hasattr(RemoteDirectoryDialog, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法缺失")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False


def verify_server_config_fix():
    """验证服务器配置编辑修复"""
    print("\n验证服务器配置编辑修复...")
    
    try:
        from config_manager import ConfigManager
        
        # 创建测试配置
        config = ConfigManager("verify_test.json")
        
        # 添加测试服务器
        server_name = "验证测试服务器"
        config.add_server(server_name, "test.example.com", 22, "testuser", "testpass")
        
        # 获取服务器配置
        server_config = config.get_server(server_name)
        
        if server_config:
            print("✓ 服务器配置获取成功")
            
            # 模拟编辑时添加名称
            server_config['name'] = server_name
            
            # 验证所有必要字段
            required_fields = ['name', 'host', 'port', 'username', 'password']
            for field in required_fields:
                if field in server_config:
                    print(f"✓ {field} 字段存在")
                else:
                    print(f"✗ {field} 字段缺失")
            
            # 清理测试文件
            if os.path.exists("verify_test.json"):
                os.remove("verify_test.json")
            if os.path.exists("key.key"):
                os.remove("key.key")
            
            return True
        else:
            print("✗ 服务器配置获取失败")
            return False
            
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False


def verify_gui_integration():
    """验证GUI集成"""
    print("\n验证GUI集成...")
    
    try:
        from gui import BackupToolGUI
        print("✓ BackupToolGUI 类导入成功")
        
        # 检查关键方法
        gui_methods = [
            'browse_remote_path',
            'edit_server',
            'create_widgets',
            'refresh_server_list'
        ]
        
        for method in gui_methods:
            if hasattr(BackupToolGUI, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法缺失")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI集成验证失败: {e}")
        return False


def verify_ssh_client_features():
    """验证SSH客户端功能"""
    print("\n验证SSH客户端功能...")
    
    try:
        from ssh_client import SSHClient
        
        client = SSHClient()
        print("✓ SSHClient 创建成功")
        
        # 检查远程目录列表功能
        if hasattr(client, 'list_remote_directory'):
            print("✓ list_remote_directory 方法存在")
        else:
            print("✗ list_remote_directory 方法缺失")
        
        # 检查其他必要方法
        required_methods = [
            'connect',
            'disconnect', 
            'download_directory',
            'upload_directory',
            'cancel_current_transfer'
        ]
        
        for method in required_methods:
            if hasattr(client, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法缺失")
        
        return True
        
    except Exception as e:
        print(f"✗ SSH客户端验证失败: {e}")
        return False


def verify_file_structure():
    """验证文件结构"""
    print("\n验证文件结构...")
    
    required_files = [
        'main.py',
        'src/gui.py',
        'src/ssh_client.py', 
        'src/config_manager.py',
        'src/backup_manager.py',
        'requirements.txt',
        'build.py',
        'README.md',
        'CHANGELOG.md'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 缺失")
            all_exist = False
    
    return all_exist


def verify_executable():
    """验证可执行文件"""
    print("\n验证可执行文件...")
    
    exe_path = "dist/RemoteBackupTool.exe"
    if os.path.exists(exe_path):
        print(f"✓ 可执行文件存在: {exe_path}")
        
        # 检查文件大小
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        print(f"✓ 文件大小: {size_mb:.1f} MB")
        
        return True
    else:
        print(f"✗ 可执行文件不存在: {exe_path}")
        return False


def main():
    """主验证函数"""
    print("=" * 60)
    print("远程文件备份工具 - 更新验证")
    print("=" * 60)
    
    verifications = [
        ("远程目录浏览功能", verify_remote_directory_dialog),
        ("服务器配置编辑修复", verify_server_config_fix),
        ("GUI集成", verify_gui_integration),
        ("SSH客户端功能", verify_ssh_client_features),
        ("文件结构", verify_file_structure),
        ("可执行文件", verify_executable)
    ]
    
    results = []
    for name, verify_func in verifications:
        try:
            result = verify_func()
            results.append((name, result))
        except Exception as e:
            print(f"✗ {name} 验证异常: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 60)
    print("验证结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 所有验证通过！新功能已成功集成。")
        print("\n新功能说明:")
        print("1. 远程目录浏览: 连接服务器后点击远程路径'浏览'按钮")
        print("2. 服务器编辑改进: 编辑时服务器名称会自动填入")
        print("3. 图形化界面: 使用树形控件显示目录结构")
        print("4. 智能导航: 支持双击进入目录，返回上级等操作")
        
        print("\n使用方法:")
        print("- 运行源码: python main.py")
        print("- 运行可执行文件: dist/RemoteBackupTool.exe")
        
    else:
        print(f"\n⚠️  有 {total - passed} 项验证失败，请检查相关功能。")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
