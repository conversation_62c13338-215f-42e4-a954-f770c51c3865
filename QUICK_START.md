# 快速开始指南

## 🚀 运行程序

```bash
# 激活虚拟环境
.\venv\Scripts\activate

# 运行程序
python main.py
```

## 📋 使用步骤

### 1. 添加服务器配置

1. 点击主界面右下角的 **"服务器管理"** 按钮
2. 在服务器管理窗口中，点击 **"添加服务器"** 按钮
3. 在弹出的对话框中填写服务器信息：
   - **服务器名称**: 自定义名称（如：我的服务器）
   - **主机地址**: 服务器IP或域名（如：*************）
   - **端口**: SSH端口（通常是22）
   - **用户名**: SSH登录用户名
   - **密码**: SSH登录密码
4. 点击 **"确定"** 保存配置
5. 可以点击 **"测试连接"** 验证配置是否正确

### 2. 配置传输

1. **源端点配置**（左侧）：
   - 选择 "本地" 或 "远程服务器"
   - 如果选择远程服务器，从下拉列表选择服务器并点击"连接"
   - 设置源路径，可以点击"浏览"选择

2. **目标端点配置**（右侧）：
   - 选择 "本地" 或 "远程服务器"
   - 配置方法同源端点
   - 设置目标路径

### 3. 传输选项

- **启用压缩**: 勾选后使用tar.gz压缩传输（推荐）
- **压缩级别**: 1-9级，6为默认值
- **传输后验证**: 可选的完整性验证

### 4. 执行传输

1. 点击 **"分析传输"** 查看传输计划和建议
2. 点击 **"开始传输"** 执行备份操作
3. 观察进度条和日志信息

## 🔧 故障排除

### 服务器添加问题
- 确保对话框窗口完全显示，可以拖拽调整大小
- 检查所有字段都已填写
- 确认网络连接正常

### 连接问题
- 验证服务器地址、端口、用户名、密码是否正确
- 确保SSH服务正在运行
- 检查防火墙设置

### 传输问题
- 确保源路径存在且有读取权限
- 确保目标路径有写入权限
- 检查磁盘空间是否充足

## 📝 支持的传输模式

1. **本地 → 本地**: 本地文件复制（支持压缩）
2. **本地 → 远程**: 上传文件到远程服务器
3. **远程 → 本地**: 从远程服务器下载文件
4. **远程 → 远程**: 服务器间传输（通过本地中转）

## 💡 使用技巧

- 大文件传输建议启用压缩
- 服务器间传输会自动启用压缩优化
- 可以随时取消正在进行的传输
- 查看操作日志了解详细信息
- 定期备份服务器配置文件

## 🆘 获取帮助

如果遇到问题：
1. 查看程序界面的操作日志
2. 检查网络连接和服务器状态
3. 验证路径和权限设置
4. 重启程序重试

---

**注意**: 首次使用请先添加服务器配置，然后测试连接确保正常后再进行文件传输。
