#!/usr/bin/env python3
"""
远程文件备份工具 V2.0 主程序
支持服务器间传输和压缩功能
"""
import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

try:
    from gui_v2 import BackupToolGUIV2
except ImportError:
    from src.gui_v2 import BackupToolGUIV2


def main():
    """主函数"""
    try:
        # 创建并运行GUI应用
        app = BackupToolGUIV2()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
