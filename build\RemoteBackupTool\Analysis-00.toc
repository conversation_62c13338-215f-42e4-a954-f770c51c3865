(['C:\\Users\\<USER>\\OneDrive\\tools\\directory transfer\\main.py'],
 ['C:\\Users\\<USER>\\OneDrive\\tools\\directory transfer'],
 ['paramiko',
  'cryptography',
  'tkinter',
  'tkinter.ttk',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'tkinter.simpledialog'],
 ['C:\\Users\\<USER>\\OneDrive\\tools\\directory '
  'transfer\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
  'transfer\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 {},
 [],
 [],
 '3.10.8 (tags/v3.10.8:aaaf517, Oct 11 2022, 16:50:30) [MSC v.1933 64 bit '
 '(AMD64)]',
 [('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory transfer\\main.py',
   'PYSOURCE')],
 [('inspect', 'C:\\Python\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('importlib',
   'C:\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing', 'C:\\Python\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python\\Python310\\lib\\contextlib.py', 'PYMODULE'),
  ('importlib._abc',
   'C:\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy', 'C:\\Python\\Python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'C:\\Python\\Python310\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python\\Python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'C:\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('copy', 'C:\\Python\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('random', 'C:\\Python\\Python310\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python\\Python310\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python\\Python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python\\Python310\\lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'C:\\Python\\Python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python\\Python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct', 'C:\\Python\\Python310\\lib\\struct.py', 'PYMODULE'),
  ('threading', 'C:\\Python\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('bisect', 'C:\\Python\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'C:\\Python\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('calendar', 'C:\\Python\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Python\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('email.charset',
   'C:\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python\\Python310\\lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python\\Python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python\\Python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'C:\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse', 'C:\\Python\\Python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('socket', 'C:\\Python\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'C:\\Python\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'C:\\Python\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'C:\\Python\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python\\Python310\\lib\\py_compile.py', 'PYMODULE'),
  ('lzma', 'C:\\Python\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'C:\\Python\\Python310\\lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'C:\\Python\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('shutil', 'C:\\Python\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('email', 'C:\\Python\\Python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python\\Python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'C:\\Python\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('token', 'C:\\Python\\Python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'C:\\Python\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Python\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Python\\Python310\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Python\\Python310\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Python\\Python310\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python\\Python310\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python\\Python310\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('hmac', 'C:\\Python\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Python\\Python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python\\Python310\\lib\\__future__.py', 'PYMODULE'),
  ('paramiko',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\__init__.py',
   'PYMODULE'),
  ('paramiko.common',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\common.py',
   'PYMODULE'),
  ('paramiko.proxy',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\proxy.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Python\\Python310\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\Python\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('shlex', 'C:\\Python\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('paramiko.config',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\config.py',
   'PYMODULE'),
  ('getpass', 'C:\\Python\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('paramiko.hostkeys',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\hostkeys.py',
   'PYMODULE'),
  ('paramiko.pkey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\pkey.py',
   'PYMODULE'),
  ('paramiko.agent',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\agent.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('paramiko.file',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\file.py',
   'PYMODULE'),
  ('paramiko.packet',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\packet.py',
   'PYMODULE'),
  ('paramiko.message',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\message.py',
   'PYMODULE'),
  ('paramiko.sftp_file',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_file.py',
   'PYMODULE'),
  ('paramiko.sftp_si',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_si.py',
   'PYMODULE'),
  ('paramiko.sftp_handle',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_handle.py',
   'PYMODULE'),
  ('paramiko.sftp_attr',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_attr.py',
   'PYMODULE'),
  ('paramiko.sftp_server',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_server.py',
   'PYMODULE'),
  ('paramiko.sftp_client',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_client.py',
   'PYMODULE'),
  ('paramiko.sftp',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp.py',
   'PYMODULE'),
  ('paramiko.ed25519key',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ed25519key.py',
   'PYMODULE'),
  ('nacl.signing',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\signing.py',
   'PYMODULE'),
  ('nacl.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\utils.py',
   'PYMODULE'),
  ('nacl.public',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\public.py',
   'PYMODULE'),
  ('nacl.exceptions',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\exceptions.py',
   'PYMODULE'),
  ('nacl.encoding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\encoding.py',
   'PYMODULE'),
  ('nacl',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\utils.py',
   'PYMODULE'),
  ('nacl.bindings.sodium_core',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\sodium_core.py',
   'PYMODULE'),
  ('nacl.bindings.randombytes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\randombytes.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_sign',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_sign.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_shorthash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_shorthash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretstream',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_secretstream.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretbox',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_secretbox.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_scalarmult',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_scalarmult.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_pwhash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_pwhash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_kx',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_kx.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_hash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_hash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_generichash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_generichash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_core',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_core.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_box',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_box.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_aead',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_aead.py',
   'PYMODULE'),
  ('paramiko.ecdsakey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ecdsakey.py',
   'PYMODULE'),
  ('paramiko.dsskey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\dsskey.py',
   'PYMODULE'),
  ('paramiko.ber',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ber.py',
   'PYMODULE'),
  ('paramiko.rsakey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\rsakey.py',
   'PYMODULE'),
  ('paramiko.server',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\server.py',
   'PYMODULE'),
  ('paramiko.ssh_exception',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ssh_exception.py',
   'PYMODULE'),
  ('paramiko.channel',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\channel.py',
   'PYMODULE'),
  ('paramiko.buffered_pipe',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\buffered_pipe.py',
   'PYMODULE'),
  ('paramiko.ssh_gss',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ssh_gss.py',
   'PYMODULE'),
  ('paramiko.auth_strategy',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\auth_strategy.py',
   'PYMODULE'),
  ('paramiko.auth_handler',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\auth_handler.py',
   'PYMODULE'),
  ('paramiko.client',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\client.py',
   'PYMODULE'),
  ('paramiko.win_openssh',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\win_openssh.py',
   'PYMODULE'),
  ('paramiko.win_pageant',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\win_pageant.py',
   'PYMODULE'),
  ('platform', 'C:\\Python\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python\\Python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('paramiko._winapi',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\_winapi.py',
   'PYMODULE'),
  ('paramiko.transport',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\transport.py',
   'PYMODULE'),
  ('paramiko.primes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\primes.py',
   'PYMODULE'),
  ('paramiko.kex_gss',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_gss.py',
   'PYMODULE'),
  ('paramiko.kex_ecdh_nist',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_ecdh_nist.py',
   'PYMODULE'),
  ('paramiko.kex_group16',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_group16.py',
   'PYMODULE'),
  ('paramiko.kex_group14',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_group14.py',
   'PYMODULE'),
  ('paramiko.kex_group1',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_group1.py',
   'PYMODULE'),
  ('paramiko.kex_gex',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_gex.py',
   'PYMODULE'),
  ('paramiko.kex_curve25519',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_curve25519.py',
   'PYMODULE'),
  ('paramiko.compress',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\compress.py',
   'PYMODULE'),
  ('paramiko.pipe',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\pipe.py',
   'PYMODULE'),
  ('paramiko.util',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\util.py',
   'PYMODULE'),
  ('paramiko._version',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\_version.py',
   'PYMODULE'),
  ('stringprep', 'C:\\Python\\Python310\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python\\Python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('src.gui',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory transfer\\src\\gui.py',
   'PYMODULE'),
  ('src',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\src\\__init__.py',
   'PYMODULE'),
  ('src.ssh_client',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\src\\ssh_client.py',
   'PYMODULE'),
  ('src.config_manager',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\src\\config_manager.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('json', 'C:\\Python\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python\\Python310\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python\\Python310\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python\\Python310\\lib\\json\\scanner.py', 'PYMODULE')],
 [('python310.dll', 'C:\\Python\\Python310\\python310.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python\\Python310\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python\\Python310\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Python\\Python310\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python\\Python310\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python\\Python310\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python\\Python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Python\\Python310\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('nacl\\_sodium.pyd',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\_sodium.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python\\Python310\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python\\Python310\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('tcl86t.dll', 'C:\\Python\\Python310\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Python\\Python310\\DLLs\\tk86t.dll', 'BINARY'),
  ('python3.dll', 'C:\\Python\\Python310\\python3.dll', 'BINARY'),
  ('libffi-7.dll', 'C:\\Python\\Python310\\DLLs\\libffi-7.dll', 'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\build\\RemoteBackupTool\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Nuuk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\auto.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\tm.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tk\\clrpick.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tk\\bgerror.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('tk\\comdlg.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tk\\menu.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tk\\text.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tk\\mkpsenc.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\NZ', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tk\\icons.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\encoding\\iso8859-11.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tk\\msgbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('tk\\scrlbar.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tk\\palette.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tk\\entry.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tk\\tkfbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tk\\tk.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kanton',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tk\\tclIndex', 'C:\\Python\\Python310\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\init.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tk\\listbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\tclIndex', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\safe.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tk\\optMenu.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tk\\scale.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tk\\dialog.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tk\\console.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Python\\Python310\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\GB', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tk\\spinbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tk\\focus.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tk\\tearoff.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('tcl\\encoding\\cns11643.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tk\\safetk.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qostanay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tk\\xmfbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\parray.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\word.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tk\\button.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\clock.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\RECORD',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography-41.0.7.dist-info\\METADATA',
   'DATA'),
  ('nacl\\py.typed',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\py.typed',
   'DATA')])
