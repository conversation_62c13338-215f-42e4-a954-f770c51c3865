"""
服务器管理窗口
V2版本的服务器配置管理界面
"""
import tkinter as tk
from tkinter import ttk, messagebox

try:
    from ..config_manager import ConfigManager
except ImportError:
    from config_manager import ConfigManager


class ServerManagerWindow:
    """服务器管理窗口"""
    
    def __init__(self, parent, config_manager, refresh_callback=None):
        self.config_manager = config_manager
        self.refresh_callback = refresh_callback
        
        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("服务器管理")
        self.window.geometry("700x500")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()
        
        # 居中显示
        self.window.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50, 
            parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
        self.refresh_server_list()
        
        # 绑定关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="服务器配置管理", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 服务器列表区域
        list_frame = ttk.LabelFrame(main_frame, text="服务器列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview
        columns = ('name', 'host', 'port', 'username')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        # 设置列标题
        self.tree.heading('name', text='服务器名称')
        self.tree.heading('host', text='主机地址')
        self.tree.heading('port', text='端口')
        self.tree.heading('username', text='用户名')
        
        # 设置列宽
        self.tree.column('name', width=150)
        self.tree.column('host', width=200)
        self.tree.column('port', width=80)
        self.tree.column('username', width=120)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', self.edit_server)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="添加服务器", command=self.add_server).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(left_buttons, text="编辑服务器", command=self.edit_server).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(left_buttons, text="删除服务器", command=self.delete_server).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(left_buttons, text="测试连接", command=self.test_connection).pack(side=tk.LEFT)
        
        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="关闭", command=self.on_closing).pack(side=tk.LEFT)
    
    def refresh_server_list(self):
        """刷新服务器列表"""
        # 清空现有内容
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 获取服务器列表
        servers = self.config_manager.list_servers()
        
        for server_name in servers:
            server_config = self.config_manager.get_server(server_name)
            if server_config:
                self.tree.insert('', 'end', values=(
                    server_name,
                    server_config['host'],
                    server_config['port'],
                    server_config['username']
                ))
    
    def add_server(self):
        """添加服务器"""
        dialog = ServerConfigDialog(self.window, "添加服务器")
        if dialog.result:
            name, host, port, username, password = dialog.result
            
            if self.config_manager.add_server(name, host, port, username, password):
                messagebox.showinfo("成功", f"服务器 '{name}' 添加成功")
                self.refresh_server_list()
                self.notify_refresh()
            else:
                messagebox.showerror("错误", "服务器名称已存在或添加失败")
    
    def edit_server(self, event=None):
        """编辑服务器"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的服务器")
            return
        
        item = selection[0]
        server_name = self.tree.item(item, 'values')[0]
        
        # 获取服务器配置
        server_config = self.config_manager.get_server(server_name)
        if not server_config:
            messagebox.showerror("错误", "获取服务器配置失败")
            return
        
        # 添加服务器名称到配置中
        server_config['name'] = server_name
        
        dialog = ServerConfigDialog(self.window, "编辑服务器", server_config)
        if dialog.result:
            new_name, host, port, username, password = dialog.result
            
            if new_name != server_name:
                # 服务器名称改变，需要删除旧的并添加新的
                self.config_manager.remove_server(server_name)
                success = self.config_manager.add_server(new_name, host, port, username, password)
            else:
                success = self.config_manager.update_server(new_name, host, port, username, password)
            
            if success:
                messagebox.showinfo("成功", f"服务器 '{new_name}' 更新成功")
                self.refresh_server_list()
                self.notify_refresh()
            else:
                messagebox.showerror("错误", "更新服务器配置失败")
    
    def delete_server(self):
        """删除服务器"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的服务器")
            return
        
        item = selection[0]
        server_name = self.tree.item(item, 'values')[0]
        
        if messagebox.askyesno("确认", f"确定要删除服务器 '{server_name}' 吗？"):
            if self.config_manager.remove_server(server_name):
                messagebox.showinfo("成功", f"服务器 '{server_name}' 删除成功")
                self.refresh_server_list()
                self.notify_refresh()
            else:
                messagebox.showerror("错误", "删除服务器失败")
    
    def test_connection(self):
        """测试连接"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要测试的服务器")
            return
        
        item = selection[0]
        server_name = self.tree.item(item, 'values')[0]
        
        # 获取服务器配置
        server_config = self.config_manager.get_server(server_name)
        if not server_config:
            messagebox.showerror("错误", "获取服务器配置失败")
            return
        
        # 创建测试连接的进度窗口
        progress_window = tk.Toplevel(self.window)
        progress_window.title("测试连接")
        progress_window.geometry("300x100")
        progress_window.transient(self.window)
        progress_window.grab_set()
        
        # 居中显示
        progress_window.geometry("+%d+%d" % (
            self.window.winfo_rootx() + 200, 
            self.window.winfo_rooty() + 200
        ))
        
        ttk.Label(progress_window, text=f"正在连接到 {server_name}...").pack(pady=20)
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill=tk.X)
        progress_bar.start()
        
        def test_connection_thread():
            try:
                from ..endpoints.remote_endpoint import RemoteEndpoint
                
                remote_endpoint = RemoteEndpoint(server_name, self.config_manager)
                success, message = remote_endpoint.connect()
                
                if success:
                    remote_endpoint.disconnect()
                
                # 更新UI
                self.window.after(0, lambda: self.on_test_result(progress_window, success, message))
                
            except Exception as e:
                self.window.after(0, lambda: self.on_test_result(progress_window, False, f"连接异常: {str(e)}"))
        
        import threading
        threading.Thread(target=test_connection_thread, daemon=True).start()
    
    def on_test_result(self, progress_window, success, message):
        """测试连接结果回调"""
        progress_window.destroy()
        
        if success:
            messagebox.showinfo("连接成功", "服务器连接正常")
        else:
            messagebox.showerror("连接失败", f"连接失败: {message}")
    
    def notify_refresh(self):
        """通知主窗口刷新"""
        if self.refresh_callback:
            self.refresh_callback()
    
    def on_closing(self):
        """关闭窗口"""
        self.window.destroy()


class ServerConfigDialog:
    """服务器配置对话框"""
    
    def __init__(self, parent, title, config=None):
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # 创建变量
        self.name_var = tk.StringVar(value=config.get('name', '') if config else '')
        self.host_var = tk.StringVar(value=config.get('host', '') if config else '')
        self.port_var = tk.StringVar(value=str(config.get('port', 22)) if config else '22')
        self.username_var = tk.StringVar(value=config.get('username', '') if config else '')
        self.password_var = tk.StringVar(value=config.get('password', '') if config else '')
        
        self.create_dialog_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def create_dialog_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 输入字段
        fields = [
            ("服务器名称:", self.name_var, False),
            ("主机地址:", self.host_var, False),
            ("端口:", self.port_var, False),
            ("用户名:", self.username_var, False),
            ("密码:", self.password_var, True)
        ]
        
        for i, (label, var, is_password) in enumerate(fields):
            ttk.Label(main_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=8)
            
            if is_password:
                entry = ttk.Entry(main_frame, textvariable=var, show="*", width=30)
            else:
                entry = ttk.Entry(main_frame, textvariable=var, width=30)
            
            entry.grid(row=i, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=8)
            
            # 第一个输入框获得焦点
            if i == 0:
                entry.focus()
        
        # 说明文本
        info_text = """
说明:
• 服务器名称: 用于识别服务器的唯一名称
• 主机地址: 服务器的IP地址或域名
• 端口: SSH服务端口，通常为22
• 用户名: SSH登录用户名
• 密码: SSH登录密码（加密存储）
        """
        
        info_label = ttk.Label(main_frame, text=info_text, 
                              font=("Arial", 9), foreground="gray")
        info_label.grid(row=len(fields), column=0, columnspan=2, pady=15, sticky=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT)
    
    def ok_clicked(self):
        """确定按钮点击"""
        # 验证输入
        if not all([self.name_var.get().strip(), self.host_var.get().strip(), 
                   self.username_var.get().strip(), self.password_var.get().strip()]):
            messagebox.showerror("错误", "请填写所有必填字段")
            return
        
        try:
            port = int(self.port_var.get())
            if port <= 0 or port > 65535:
                raise ValueError()
        except ValueError:
            messagebox.showerror("错误", "端口必须是1-65535之间的数字")
            return
        
        self.result = (
            self.name_var.get().strip(),
            self.host_var.get().strip(),
            port,
            self.username_var.get().strip(),
            self.password_var.get().strip()
        )
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()
