#!/usr/bin/env python3
"""
使用示例脚本
演示如何使用备份工具的各个功能
"""
import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from config_manager import ConfigManager
from ssh_client import SSHClient
from backup_manager import BackupManager


def demo_config_management():
    """演示配置管理功能"""
    print("=== 配置管理演示 ===")
    
    # 创建配置管理器
    config = ConfigManager("demo_servers.json")
    
    # 添加示例服务器
    servers = [
        ("开发服务器", "*************", 22, "developer", "dev_password"),
        ("生产服务器", "prod.example.com", 2222, "admin", "prod_password"),
        ("测试服务器", "test.local", 22, "tester", "test_password")
    ]
    
    for name, host, port, username, password in servers:
        success = config.add_server(name, host, port, username, password)
        print(f"添加服务器 '{name}': {'成功' if success else '失败'}")
    
    # 列出所有服务器
    server_list = config.list_servers()
    print(f"\n已配置的服务器: {server_list}")
    
    # 获取服务器详情
    for server_name in server_list:
        server_info = config.get_server(server_name)
        if server_info:
            print(f"\n{server_name}:")
            print(f"  主机: {server_info['host']}")
            print(f"  端口: {server_info['port']}")
            print(f"  用户名: {server_info['username']}")
            print(f"  密码: {'*' * len(server_info['password'])}")
    
    # 清理演示文件
    if os.path.exists("demo_servers.json"):
        os.remove("demo_servers.json")
    if os.path.exists("key.key"):
        os.remove("key.key")
    
    print("\n配置管理演示完成\n")


def demo_backup_manager():
    """演示备份管理功能"""
    print("=== 备份管理演示 ===")
    
    # 创建备份管理器
    manager = BackupManager()
    
    # 显示连接状态
    status = manager.get_connection_status()
    print(f"当前连接状态: {status}")
    
    # 模拟备份历史
    print("\n备份历史功能演示:")
    history = manager.get_backup_history()
    print(f"历史记录数量: {len(history)}")
    
    # 路径验证演示
    print("\n路径验证演示:")
    valid, message = manager.validate_paths("C:\\temp", "/home/<USER>/backup", "upload")
    print(f"路径验证结果: {message}")
    
    print("备份管理演示完成\n")


def demo_ssh_features():
    """演示SSH功能特性"""
    print("=== SSH功能演示 ===")
    
    # 创建SSH客户端
    client = SSHClient()
    
    print(f"初始连接状态: {client.connected}")
    print("SSH客户端功能:")
    print("- 支持SSH连接和认证")
    print("- 支持SFTP文件传输")
    print("- 支持目录递归传输")
    print("- 支持传输进度回调")
    print("- 支持传输取消功能")
    
    # 演示进度回调函数
    def progress_callback(current, total):
        if total > 0:
            percent = (current / total) * 100
            print(f"传输进度: {percent:.1f}% ({current}/{total})")
    
    print(f"\n进度回调函数示例: {progress_callback.__name__}")
    
    # 清理
    client.disconnect()
    print("SSH功能演示完成\n")


def show_project_structure():
    """显示项目结构"""
    print("=== 项目结构 ===")
    
    structure = """
远程文件备份工具/
├── src/                    # 源代码目录
│   ├── __init__.py        # 包初始化文件
│   ├── config_manager.py  # 配置管理模块
│   ├── ssh_client.py      # SSH客户端模块
│   ├── backup_manager.py  # 备份管理模块
│   └── gui.py            # 图形界面模块
├── main.py               # 主程序入口
├── test_modules.py       # 模块测试脚本
├── example_usage.py      # 使用示例脚本
├── build.py             # 打包脚本
├── build.bat            # Windows打包批处理
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明文档
├── venv/               # 虚拟环境目录
└── dist/               # 打包输出目录
    ├── RemoteBackupTool.exe  # 可执行文件
    └── README.txt           # 使用说明
"""
    
    print(structure)


def show_usage_tips():
    """显示使用技巧"""
    print("=== 使用技巧 ===")
    
    tips = [
        "1. 首次使用前，建议先测试SSH连接",
        "2. 大文件传输时，注意网络稳定性",
        "3. 定期备份服务器配置文件",
        "4. 使用相对路径时要注意当前工作目录",
        "5. 传输过程中可以随时取消操作",
        "6. 查看操作日志了解详细信息",
        "7. 密码会自动加密存储，确保安全",
        "8. 支持多服务器配置，方便管理",
        "9. 可以打包成独立exe文件分发",
        "10. 遇到问题时查看README文档"
    ]
    
    for tip in tips:
        print(f"  {tip}")
    
    print()


def main():
    """主演示函数"""
    print("远程文件备份工具 - 功能演示")
    print("=" * 50)
    
    # 运行各个演示
    demo_config_management()
    demo_backup_manager()
    demo_ssh_features()
    show_project_structure()
    show_usage_tips()
    
    print("=== 演示完成 ===")
    print("\n要运行实际程序，请执行:")
    print("  python main.py")
    print("\n要打包程序，请执行:")
    print("  python build.py")
    print("  或者运行: build.bat")
    print("\n要测试模块，请执行:")
    print("  python test_modules.py")


if __name__ == "__main__":
    main()
