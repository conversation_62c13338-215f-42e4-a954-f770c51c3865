('C:\\Users\\<USER>\\OneDrive\\tools\\directory '
 'transfer\\build\\RemoteBackupTool\\PYZ-00.pyz',
 [('__future__', 'C:\\Python\\Python310\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'C:\\Python\\Python310\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python\\Python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Python\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bisect', 'C:\\Python\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Python\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python\\Python310\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python\\Python310\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('csv', 'C:\\Python\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python\\Python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Python\\Python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Python\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Python\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\Python\\Python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'C:\\Python\\Python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python\\Python310\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser', 'C:\\Python\\Python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python\\Python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'C:\\Python\\Python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'C:\\Python\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Python\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('importlib',
   'C:\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Python\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python\\Python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Python\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python\\Python310\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python\\Python310\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python\\Python310\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python\\Python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('nacl',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_aead',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_aead.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_box',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_box.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_core',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_core.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_generichash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_generichash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_hash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_hash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_kx',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_kx.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_pwhash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_pwhash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_scalarmult',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_scalarmult.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretbox',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_secretbox.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretstream',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_secretstream.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_shorthash',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_shorthash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_sign',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\crypto_sign.py',
   'PYMODULE'),
  ('nacl.bindings.randombytes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\randombytes.py',
   'PYMODULE'),
  ('nacl.bindings.sodium_core',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\sodium_core.py',
   'PYMODULE'),
  ('nacl.bindings.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\bindings\\utils.py',
   'PYMODULE'),
  ('nacl.encoding',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\encoding.py',
   'PYMODULE'),
  ('nacl.exceptions',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\exceptions.py',
   'PYMODULE'),
  ('nacl.public',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\public.py',
   'PYMODULE'),
  ('nacl.signing',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\signing.py',
   'PYMODULE'),
  ('nacl.utils',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\nacl\\utils.py',
   'PYMODULE'),
  ('numbers', 'C:\\Python\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Python\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Python\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('paramiko',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\__init__.py',
   'PYMODULE'),
  ('paramiko._version',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\_version.py',
   'PYMODULE'),
  ('paramiko._winapi',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\_winapi.py',
   'PYMODULE'),
  ('paramiko.agent',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\agent.py',
   'PYMODULE'),
  ('paramiko.auth_handler',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\auth_handler.py',
   'PYMODULE'),
  ('paramiko.auth_strategy',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\auth_strategy.py',
   'PYMODULE'),
  ('paramiko.ber',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ber.py',
   'PYMODULE'),
  ('paramiko.buffered_pipe',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\buffered_pipe.py',
   'PYMODULE'),
  ('paramiko.channel',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\channel.py',
   'PYMODULE'),
  ('paramiko.client',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\client.py',
   'PYMODULE'),
  ('paramiko.common',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\common.py',
   'PYMODULE'),
  ('paramiko.compress',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\compress.py',
   'PYMODULE'),
  ('paramiko.config',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\config.py',
   'PYMODULE'),
  ('paramiko.dsskey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\dsskey.py',
   'PYMODULE'),
  ('paramiko.ecdsakey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ecdsakey.py',
   'PYMODULE'),
  ('paramiko.ed25519key',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ed25519key.py',
   'PYMODULE'),
  ('paramiko.file',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\file.py',
   'PYMODULE'),
  ('paramiko.hostkeys',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\hostkeys.py',
   'PYMODULE'),
  ('paramiko.kex_curve25519',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_curve25519.py',
   'PYMODULE'),
  ('paramiko.kex_ecdh_nist',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_ecdh_nist.py',
   'PYMODULE'),
  ('paramiko.kex_gex',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_gex.py',
   'PYMODULE'),
  ('paramiko.kex_group1',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_group1.py',
   'PYMODULE'),
  ('paramiko.kex_group14',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_group14.py',
   'PYMODULE'),
  ('paramiko.kex_group16',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_group16.py',
   'PYMODULE'),
  ('paramiko.kex_gss',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\kex_gss.py',
   'PYMODULE'),
  ('paramiko.message',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\message.py',
   'PYMODULE'),
  ('paramiko.packet',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\packet.py',
   'PYMODULE'),
  ('paramiko.pipe',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\pipe.py',
   'PYMODULE'),
  ('paramiko.pkey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\pkey.py',
   'PYMODULE'),
  ('paramiko.primes',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\primes.py',
   'PYMODULE'),
  ('paramiko.proxy',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\proxy.py',
   'PYMODULE'),
  ('paramiko.rsakey',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\rsakey.py',
   'PYMODULE'),
  ('paramiko.server',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\server.py',
   'PYMODULE'),
  ('paramiko.sftp',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp.py',
   'PYMODULE'),
  ('paramiko.sftp_attr',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_attr.py',
   'PYMODULE'),
  ('paramiko.sftp_client',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_client.py',
   'PYMODULE'),
  ('paramiko.sftp_file',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_file.py',
   'PYMODULE'),
  ('paramiko.sftp_handle',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_handle.py',
   'PYMODULE'),
  ('paramiko.sftp_server',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_server.py',
   'PYMODULE'),
  ('paramiko.sftp_si',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\sftp_si.py',
   'PYMODULE'),
  ('paramiko.ssh_exception',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ssh_exception.py',
   'PYMODULE'),
  ('paramiko.ssh_gss',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\ssh_gss.py',
   'PYMODULE'),
  ('paramiko.transport',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\transport.py',
   'PYMODULE'),
  ('paramiko.util',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\util.py',
   'PYMODULE'),
  ('paramiko.win_openssh',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\win_openssh.py',
   'PYMODULE'),
  ('paramiko.win_pageant',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\venv\\lib\\site-packages\\paramiko\\win_pageant.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Python\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('platform', 'C:\\Python\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Python\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python\\Python310\\lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'C:\\Python\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python\\Python310\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\Python\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'C:\\Python\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Python\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('src',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\src\\__init__.py',
   'PYMODULE'),
  ('src.config_manager',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\src\\config_manager.py',
   'PYMODULE'),
  ('src.gui',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory transfer\\src\\gui.py',
   'PYMODULE'),
  ('src.ssh_client',
   'C:\\Users\\<USER>\\OneDrive\\tools\\directory '
   'transfer\\src\\ssh_client.py',
   'PYMODULE'),
  ('statistics', 'C:\\Python\\Python310\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python\\Python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python\\Python310\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python\\Python310\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python\\Python310\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Python\\Python310\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Python\\Python310\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Python\\Python310\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python\\Python310\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'C:\\Python\\Python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python\\Python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'C:\\Python\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('urllib', 'C:\\Python\\Python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python\\Python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('uu', 'C:\\Python\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE')])
