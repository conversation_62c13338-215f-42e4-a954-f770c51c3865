#!/usr/bin/env python3
"""
PyInstaller打包脚本
用于将Python程序打包成独立的可执行文件
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            try:
                shutil.rmtree(dir_name)
            except PermissionError:
                print(f"警告: 无法删除 {dir_name}，可能有文件正在使用")
                # 尝试重命名目录
                import time
                backup_name = f"{dir_name}_backup_{int(time.time())}"
                try:
                    os.rename(dir_name, backup_name)
                    print(f"已将 {dir_name} 重命名为 {backup_name}")
                except:
                    print(f"跳过清理 {dir_name}")
            except Exception as e:
                print(f"清理 {dir_name} 时出错: {e}")
    
    # 清理.spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        print(f"删除spec文件: {spec_file}")
        os.remove(spec_file)


def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'paramiko',
        'cryptography',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'tarfile',
        'tempfile',
        'threading'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RemoteBackupTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以在这里指定图标文件
)
'''

    with open('RemoteBackupTool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("已创建 RemoteBackupTool.spec 文件")


def build_executable():
    """构建可执行文件"""
    print("开始构建远程文件备份工具...")

    # 使用spec文件构建
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        'RemoteBackupTool.spec'
    ]

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("构建成功!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def copy_additional_files():
    """复制额外的文件到dist目录"""
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("dist目录不存在，跳过文件复制")
        return
    
    # 创建示例配置文件
    readme_content = """# 远程文件备份工具

## 使用说明

1. 首次运行程序时，点击"添加服务器"按钮添加SSH服务器配置
2. 选择服务器后，点击"测试连接"确保连接正常
3. 设置本地路径和远程路径
4. 选择传输模式（下载或上传）
5. 点击"开始传输"执行备份操作

## 注意事项

- 程序会自动创建配置文件来保存服务器信息
- 密码使用加密存储，确保安全性
- 支持断点续传和进度显示
- 可以查看传输历史记录

## 文件说明

- servers.json: 服务器配置文件（加密存储）
- key.key: 加密密钥文件
- backup_history.json: 备份历史记录

## 技术支持

如有问题，请检查日志输出或联系技术支持。
"""
    
    readme_file = dist_dir / 'README.txt'
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"已创建说明文件: {readme_file}")


def main():
    """主函数"""
    print("=== 远程文件备份工具打包脚本 ===")
    
    # 检查是否在虚拟环境中
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("警告: 建议在虚拟环境中运行打包脚本")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            return
    
    # 检查依赖
    try:
        import PyInstaller
        print(f"PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("错误: 未安装PyInstaller")
        print("请运行: pip install pyinstaller")
        return
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建spec文件
    create_spec_file()
    
    # 构建可执行文件
    if build_executable():
        # 复制额外文件
        copy_additional_files()
        
        print("\n=== 构建完成 ===")
        print("可执行文件位置: dist/RemoteBackupTool.exe")
        print("请测试可执行文件是否正常工作")
    else:
        print("\n=== 构建失败 ===")
        print("请检查错误信息并修复问题")


if __name__ == "__main__":
    main()
