"""
端点基类
定义传输端点的通用接口
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Optional, Callable


class BaseEndpoint(ABC):
    """传输端点基类"""
    
    def __init__(self, endpoint_type: str):
        self.endpoint_type = endpoint_type  # "local" 或 "remote"
        self.connected = False
    
    @abstractmethod
    def connect(self) -> Tuple[bool, str]:
        """
        连接到端点
        
        Returns:
            (成功标志, 消息)
        """
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass
    
    @abstractmethod
    def list_directory(self, path: str) -> Tuple[bool, List[Dict]]:
        """
        列出目录内容
        
        Args:
            path: 目录路径
            
        Returns:
            (成功标志, 文件列表)
        """
        pass
    
    @abstractmethod
    def path_exists(self, path: str) -> bool:
        """检查路径是否存在"""
        pass
    
    @abstractmethod
    def is_directory(self, path: str) -> bool:
        """检查是否为目录"""
        pass
    
    @abstractmethod
    def get_path_info(self, path: str) -> Dict:
        """
        获取路径信息
        
        Returns:
            包含大小、修改时间等信息的字典
        """
        pass
    
    @abstractmethod
    def create_directory(self, path: str) -> bool:
        """创建目录"""
        pass
    
    @abstractmethod
    def remove_path(self, path: str) -> bool:
        """删除文件或目录"""
        pass
    
    def get_display_name(self) -> str:
        """获取端点显示名称"""
        return self.endpoint_type.title()
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connected
