# 远程文件备份工具 V2.0 架构设计

## 概述

V2.0版本将支持以下新功能：
1. 文件压缩传输（tar.gz格式）
2. 服务器间直接传输
3. 灵活的源端和目标端配置

## 核心概念

### 传输端点（Endpoint）
- **本地端点（Local）**: 本机文件系统
- **远程端点（Remote）**: SSH连接的远程服务器

### 传输模式
1. **本地 → 本地**: 本地文件复制（带压缩选项）
2. **本地 → 远程**: 上传到远程服务器
3. **远程 → 本地**: 从远程服务器下载
4. **远程 → 远程**: 服务器间直接传输

### 压缩选项
- **启用压缩**: 传输前压缩为tar.gz，传输后解压
- **禁用压缩**: 直接传输原始文件

## 新架构组件

### 1. 端点管理器（EndpointManager）
```python
class EndpointManager:
    - LocalEndpoint: 管理本地文件操作
    - RemoteEndpoint: 管理远程SSH连接和文件操作
```

### 2. 压缩管理器（CompressionManager）
```python
class CompressionManager:
    - compress_directory(): 压缩目录为tar.gz
    - decompress_archive(): 解压tar.gz文件
    - get_compression_info(): 获取压缩信息
```

### 3. 传输协调器（TransferCoordinator）
```python
class TransferCoordinator:
    - plan_transfer(): 规划传输策略
    - execute_transfer(): 执行传输
    - handle_compression(): 处理压缩/解压
```

### 4. 新GUI设计
```
┌─────────────────────────────────────────────────────────┐
│                   远程文件备份工具 V2.0                    │
├─────────────────────────────────────────────────────────┤
│ 源端配置                    │ 目标端配置                   │
│ ○ 本地  ○ 远程服务器         │ ○ 本地  ○ 远程服务器          │
│ 服务器: [下拉选择]           │ 服务器: [下拉选择]            │
│ 路径: [路径输入] [浏览]      │ 路径: [路径输入] [浏览]       │
├─────────────────────────────────────────────────────────┤
│ 传输选项                                                 │
│ ☑ 启用压缩 (tar.gz)                                     │
│ ☑ 传输后验证                                            │
├─────────────────────────────────────────────────────────┤
│ 进度显示                                                 │
│ [进度条]                                                │
│ 状态: 准备就绪                                           │
├─────────────────────────────────────────────────────────┤
│ 操作日志                                                 │
│ [日志文本区域]                                           │
├─────────────────────────────────────────────────────────┤
│ [开始传输] [取消传输] [清空日志]                          │
└─────────────────────────────────────────────────────────┘
```

## 传输流程设计

### 1. 本地 → 远程（启用压缩）
```
1. 压缩本地目录 → temp.tar.gz
2. 上传 temp.tar.gz 到远程临时目录
3. 在远程服务器解压到目标目录
4. 清理临时文件
```

### 2. 远程 → 本地（启用压缩）
```
1. 在远程服务器压缩目录 → temp.tar.gz
2. 下载 temp.tar.gz 到本地临时目录
3. 在本地解压到目标目录
4. 清理临时文件
```

### 3. 远程 → 远程（启用压缩）
```
1. 在源服务器压缩目录 → temp.tar.gz
2. 下载到本地临时目录
3. 上传到目标服务器临时目录
4. 在目标服务器解压到目标目录
5. 清理所有临时文件
```

### 4. 远程 → 远程（直接传输，高级功能）
```
1. 在源服务器建立到目标服务器的SSH隧道
2. 使用scp或rsync直接传输
3. 可选压缩传输
```

## 文件结构调整

### 新增模块
```
src/
├── endpoints/
│   ├── __init__.py
│   ├── base_endpoint.py      # 端点基类
│   ├── local_endpoint.py     # 本地端点
│   └── remote_endpoint.py    # 远程端点
├── compression/
│   ├── __init__.py
│   └── tar_compression.py    # tar.gz压缩管理
├── transfer/
│   ├── __init__.py
│   ├── transfer_coordinator.py  # 传输协调器
│   └── transfer_strategies.py   # 传输策略
└── gui_v2/
    ├── __init__.py
    ├── main_window.py        # 新主窗口
    ├── endpoint_panel.py     # 端点配置面板
    └── transfer_panel.py     # 传输控制面板
```

## 配置文件扩展

### 传输配置
```json
{
  "transfer_settings": {
    "default_compression": true,
    "temp_directory": "/tmp/backup_tool",
    "verify_after_transfer": true,
    "max_parallel_transfers": 3
  },
  "compression_settings": {
    "compression_level": 6,
    "exclude_patterns": ["*.tmp", "*.log"],
    "preserve_permissions": true
  }
}
```

## 兼容性考虑

### 向后兼容
- 保留原有的简单模式界面选项
- 支持从V1.x配置文件升级
- 保持原有API接口

### 渐进式升级
- 用户可以选择使用新界面或保持原界面
- 新功能作为可选项提供
- 逐步引导用户使用新功能

## 性能优化

### 压缩优化
- 多线程压缩/解压
- 流式压缩（边压缩边传输）
- 智能压缩级别选择

### 传输优化
- 断点续传支持
- 并行文件传输
- 网络带宽自适应

## 安全考虑

### 临时文件安全
- 临时文件加密存储
- 传输完成后安全删除
- 权限控制

### 服务器间传输安全
- SSH密钥认证
- 传输加密
- 连接验证

## 实施计划

### 阶段1: 基础架构
1. 创建新的模块结构
2. 实现端点管理器
3. 实现压缩管理器

### 阶段2: 传输功能
1. 实现传输协调器
2. 支持本地↔远程传输（带压缩）
3. 基础GUI改进

### 阶段3: 高级功能
1. 实现远程↔远程传输
2. 完整的新GUI
3. 性能优化

### 阶段4: 测试和优化
1. 全面功能测试
2. 性能测试和优化
3. 用户体验改进
