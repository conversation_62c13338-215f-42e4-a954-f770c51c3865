#!/usr/bin/env python3
"""
V2.0 功能测试脚本
测试压缩传输和服务器间传输功能
"""
import sys
import os
import tempfile
import shutil

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)


def test_compression_manager():
    """测试压缩管理器"""
    print("=== 测试压缩管理器 ===")
    
    try:
        from compression.tar_compression import TarCompressionManager
        
        # 创建测试目录和文件
        test_dir = tempfile.mkdtemp(prefix="test_compression_")
        test_file1 = os.path.join(test_dir, "test1.txt")
        test_file2 = os.path.join(test_dir, "test2.txt")
        
        with open(test_file1, 'w') as f:
            f.write("This is test file 1\n" * 100)
        with open(test_file2, 'w') as f:
            f.write("This is test file 2\n" * 200)
        
        print(f"创建测试目录: {test_dir}")
        
        # 测试压缩
        compression_manager = TarCompressionManager()
        
        def progress_callback(current, total, filename):
            print(f"压缩进度: {current}/{total} - {filename}")
        
        success, message, archive_path = compression_manager.compress_directory(
            test_dir, progress_callback=progress_callback
        )
        
        if success:
            print(f"✓ 压缩成功: {message}")
            print(f"  压缩文件: {archive_path}")
            
            # 测试解压
            extract_dir = tempfile.mkdtemp(prefix="test_extract_")
            
            def extract_progress_callback(current, total, filename):
                print(f"解压进度: {current}/{total} - {filename}")
            
            success, message = compression_manager.decompress_archive(
                archive_path, extract_dir, extract_progress_callback
            )
            
            if success:
                print(f"✓ 解压成功: {message}")
                print(f"  解压目录: {extract_dir}")
                
                # 验证文件
                extracted_files = os.listdir(extract_dir)
                print(f"  解压文件: {extracted_files}")
            else:
                print(f"✗ 解压失败: {message}")
            
            # 清理
            if os.path.exists(extract_dir):
                shutil.rmtree(extract_dir)
        else:
            print(f"✗ 压缩失败: {message}")
        
        # 清理
        compression_manager.cleanup_temp_files()
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        print("压缩管理器测试完成\n")
        return True
        
    except Exception as e:
        print(f"✗ 压缩管理器测试失败: {e}")
        return False


def test_endpoints():
    """测试端点管理"""
    print("=== 测试端点管理 ===")
    
    try:
        from endpoints.local_endpoint import LocalEndpoint
        from endpoints.remote_endpoint import RemoteEndpoint
        from config_manager import ConfigManager
        
        # 测试本地端点
        local_endpoint = LocalEndpoint()
        success, message = local_endpoint.connect()
        print(f"本地端点连接: {'✓' if success else '✗'} {message}")
        
        # 测试目录列表
        home_dir = local_endpoint.get_home_directory()
        success, files = local_endpoint.list_directory(home_dir)
        if success:
            print(f"✓ 本地目录列表成功，共 {len(files)} 个项目")
        else:
            print(f"✗ 本地目录列表失败: {files}")
        
        # 测试远程端点（需要配置）
        config_manager = ConfigManager()
        servers = config_manager.list_servers()
        
        if servers:
            print(f"发现 {len(servers)} 个配置的服务器")
            # 这里可以测试远程连接，但需要真实的服务器配置
        else:
            print("未发现配置的服务器，跳过远程端点测试")
        
        print("端点管理测试完成\n")
        return True
        
    except Exception as e:
        print(f"✗ 端点管理测试失败: {e}")
        return False


def test_transfer_coordinator():
    """测试传输协调器"""
    print("=== 测试传输协调器 ===")
    
    try:
        from transfer.transfer_coordinator import TransferCoordinator, TransferMode
        from endpoints.local_endpoint import LocalEndpoint
        
        coordinator = TransferCoordinator()
        
        # 创建测试文件
        test_dir1 = tempfile.mkdtemp(prefix="test_source_")
        test_dir2 = tempfile.mkdtemp(prefix="test_target_")
        
        test_file = os.path.join(test_dir1, "test.txt")
        with open(test_file, 'w') as f:
            f.write("Test content for transfer\n" * 50)
        
        print(f"创建测试源目录: {test_dir1}")
        print(f"创建测试目标目录: {test_dir2}")
        
        # 创建端点
        source_endpoint = LocalEndpoint()
        target_endpoint = LocalEndpoint()
        source_endpoint.connect()
        target_endpoint.connect()
        
        # 测试本地到本地传输（带压缩）
        plan = coordinator.plan_transfer(
            source_endpoint, test_dir1,
            target_endpoint, os.path.join(test_dir2, "transferred"),
            use_compression=True
        )
        
        print(f"传输计划: {plan['mode']}")
        
        def progress_callback(current, total, message=""):
            print(f"传输进度: {current:.1f}% - {message}")
        
        success, message = coordinator.execute_transfer(plan, progress_callback)
        
        if success:
            print(f"✓ 传输成功: {message}")
            
            # 验证传输结果
            target_file = os.path.join(test_dir2, "transferred", "test.txt")
            if os.path.exists(target_file):
                print("✓ 文件传输验证成功")
            else:
                print("✗ 文件传输验证失败")
        else:
            print(f"✗ 传输失败: {message}")
        
        # 清理
        if os.path.exists(test_dir1):
            shutil.rmtree(test_dir1)
        if os.path.exists(test_dir2):
            shutil.rmtree(test_dir2)
        
        print("传输协调器测试完成\n")
        return True
        
    except Exception as e:
        print(f"✗ 传输协调器测试失败: {e}")
        return False


def test_transfer_strategies():
    """测试传输策略"""
    print("=== 测试传输策略 ===")
    
    try:
        from transfer.transfer_strategies import (
            CompressionStrategy, NetworkStrategy, StrategyFactory
        )
        from transfer.transfer_coordinator import TransferMode
        
        # 测试压缩策略
        should_compress = CompressionStrategy.should_use_compression(10 * 1024 * 1024)  # 10MB
        print(f"10MB文件建议压缩: {should_compress}")
        
        compression_ratio = CompressionStrategy.estimate_compression_ratio(['.txt', '.log'])
        print(f"文本文件估算压缩比: {compression_ratio}")
        
        # 测试网络策略
        speed = NetworkStrategy.estimate_transfer_speed(("local", "remote"))
        print(f"网络传输估算速度: {speed / (1024*1024):.1f} MB/s")
        
        transfer_time = NetworkStrategy.calculate_transfer_time(100 * 1024 * 1024, speed, 0.6)
        print(f"100MB文件传输估算时间: {transfer_time} 秒")
        
        # 测试策略工厂
        strategy = StrategyFactory.get_strategy(TransferMode.LOCAL_TO_LOCAL)
        print(f"本地传输策略: {strategy.__class__.__name__}")
        
        # 模拟传输计划分析
        from endpoints.local_endpoint import LocalEndpoint

        source_endpoint = LocalEndpoint()
        target_endpoint = LocalEndpoint()
        source_endpoint.connect()
        target_endpoint.connect()

        mock_plan = {
            'mode': TransferMode.LOCAL_TO_REMOTE,
            'source_endpoint': source_endpoint,
            'target_endpoint': target_endpoint,
            'source_path': '/test/path',
            'target_path': '/test/target',
            'source_size': 50 * 1024 * 1024,  # 50MB
            'use_compression': False
        }
        
        analysis = StrategyFactory.analyze_transfer(mock_plan)
        print(f"传输分析结果:")
        print(f"  有效: {analysis['valid']}")
        print(f"  估算时间: {analysis['estimated_time']} 秒")
        print(f"  建议: {analysis['recommendations']}")
        
        print("传输策略测试完成\n")
        return True
        
    except Exception as e:
        print(f"✗ 传输策略测试失败: {e}")
        return False


def test_gui_v2_imports():
    """测试GUI V2模块导入"""
    print("=== 测试GUI V2模块导入 ===")
    
    try:
        import tkinter as tk
        print("✓ tkinter导入成功")
        
        from gui_v2.main_window import BackupToolGUIV2
        print("✓ BackupToolGUIV2导入成功")
        
        from gui_v2.endpoint_panel import EndpointPanel
        print("✓ EndpointPanel导入成功")
        
        from gui_v2.transfer_panel import TransferPanel
        print("✓ TransferPanel导入成功")
        
        print("GUI V2模块导入测试完成\n")
        return True
        
    except Exception as e:
        print(f"✗ GUI V2模块导入测试失败: {e}")
        return False


def show_v2_features():
    """显示V2.0新功能"""
    print("=== V2.0 新功能特性 ===")
    
    features = [
        "1. 文件压缩传输 (tar.gz)",
        "   - 支持可调压缩级别 (1-9)",
        "   - 智能压缩建议",
        "   - 流式压缩处理",
        "",
        "2. 灵活的端点配置",
        "   - 本地端点: 本机文件系统",
        "   - 远程端点: SSH服务器",
        "   - 图形化路径浏览",
        "",
        "3. 多种传输模式",
        "   - 本地 → 本地 (带压缩选项)",
        "   - 本地 → 远程 (上传)",
        "   - 远程 → 本地 (下载)",
        "   - 远程 → 远程 (服务器间传输)",
        "",
        "4. 智能传输策略",
        "   - 自动优化传输计划",
        "   - 传输时间估算",
        "   - 个性化建议",
        "",
        "5. 增强的用户界面",
        "   - 源端点和目标端点配置",
        "   - 实时传输分析",
        "   - 详细进度显示",
        "",
        "6. 高级功能",
        "   - 传输验证选项",
        "   - 临时文件自动清理",
        "   - 错误恢复机制"
    ]
    
    for feature in features:
        print(feature)
    
    print("\n使用方法:")
    print("- 运行V2.0界面: python main_v2.py")
    print("- 运行经典界面: python main.py")
    print("- 功能测试: python test_v2_features.py")


def main():
    """主测试函数"""
    print("远程文件备份工具 V2.0 - 功能测试")
    print("=" * 60)
    
    tests = [
        ("压缩管理器", test_compression_manager),
        ("端点管理", test_endpoints),
        ("传输协调器", test_transfer_coordinator),
        ("传输策略", test_transfer_strategies),
        ("GUI V2模块", test_gui_v2_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！V2.0功能已成功实现。")
    else:
        print(f"\n⚠️  有 {total - passed} 项测试失败，请检查相关功能。")
    
    print("\n" + "=" * 60)
    show_v2_features()


if __name__ == "__main__":
    main()
