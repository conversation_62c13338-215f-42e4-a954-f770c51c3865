"""
备份管理器
整合SSH客户端和配置管理功能
"""
import os
import json
import datetime
from typing import Dict, List, Optional, Callable, Tuple
try:
    from .ssh_client import SSHClient
    from .config_manager import ConfigManager
except ImportError:
    from ssh_client import SSHClient
    from config_manager import ConfigManager


class BackupManager:
    """备份管理器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.ssh_client = SSHClient()
        self.backup_history_file = "backup_history.json"
        self.current_session = None
    
    def connect_to_server(self, server_name: str) -> Tuple[bool, str]:
        """连接到指定服务器"""
        server_config = self.config_manager.get_server(server_name)
        if not server_config:
            return False, f"服务器 '{server_name}' 不存在"
        
        success, message = self.ssh_client.connect(
            server_config['host'],
            server_config['port'],
            server_config['username'],
            server_config['password']
        )
        
        if success:
            self.current_session = {
                'server_name': server_name,
                'connected_at': datetime.datetime.now().isoformat(),
                'server_config': server_config
            }
        
        return success, message
    
    def disconnect(self):
        """断开连接"""
        self.ssh_client.disconnect()
        self.current_session = None
    
    def backup_to_local(self, remote_path: str, local_path: str, 
                       progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """备份远程文件到本地"""
        if not self.ssh_client.connected:
            return False, "未连接到服务器"
        
        # 记录备份开始
        backup_record = {
            'type': 'download',
            'remote_path': remote_path,
            'local_path': local_path,
            'server_name': self.current_session['server_name'] if self.current_session else 'unknown',
            'start_time': datetime.datetime.now().isoformat(),
            'status': 'in_progress'
        }
        
        try:
            # 执行备份
            success, message = self.ssh_client.download_directory(
                remote_path, local_path, progress_callback
            )
            
            # 更新备份记录
            backup_record['end_time'] = datetime.datetime.now().isoformat()
            backup_record['status'] = 'completed' if success else 'failed'
            backup_record['message'] = message
            
            # 保存备份历史
            self._save_backup_record(backup_record)
            
            return success, message
            
        except Exception as e:
            backup_record['end_time'] = datetime.datetime.now().isoformat()
            backup_record['status'] = 'error'
            backup_record['message'] = str(e)
            self._save_backup_record(backup_record)
            return False, f"备份过程中出错: {str(e)}"
    
    def restore_to_remote(self, local_path: str, remote_path: str, 
                         progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """恢复本地文件到远程"""
        if not self.ssh_client.connected:
            return False, "未连接到服务器"
        
        # 记录恢复开始
        backup_record = {
            'type': 'upload',
            'local_path': local_path,
            'remote_path': remote_path,
            'server_name': self.current_session['server_name'] if self.current_session else 'unknown',
            'start_time': datetime.datetime.now().isoformat(),
            'status': 'in_progress'
        }
        
        try:
            # 执行恢复
            success, message = self.ssh_client.upload_directory(
                local_path, remote_path, progress_callback
            )
            
            # 更新备份记录
            backup_record['end_time'] = datetime.datetime.now().isoformat()
            backup_record['status'] = 'completed' if success else 'failed'
            backup_record['message'] = message
            
            # 保存备份历史
            self._save_backup_record(backup_record)
            
            return success, message
            
        except Exception as e:
            backup_record['end_time'] = datetime.datetime.now().isoformat()
            backup_record['status'] = 'error'
            backup_record['message'] = str(e)
            self._save_backup_record(backup_record)
            return False, f"恢复过程中出错: {str(e)}"
    
    def _save_backup_record(self, record: Dict):
        """保存备份记录"""
        try:
            # 加载现有历史
            history = self._load_backup_history()
            
            # 添加新记录
            history.append(record)
            
            # 只保留最近100条记录
            if len(history) > 100:
                history = history[-100:]
            
            # 保存到文件
            with open(self.backup_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存备份记录失败: {e}")
    
    def _load_backup_history(self) -> List[Dict]:
        """加载备份历史"""
        if not os.path.exists(self.backup_history_file):
            return []
        
        try:
            with open(self.backup_history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载备份历史失败: {e}")
            return []
    
    def get_backup_history(self, limit: int = 20) -> List[Dict]:
        """获取备份历史"""
        history = self._load_backup_history()
        return history[-limit:] if limit > 0 else history
    
    def clear_backup_history(self) -> bool:
        """清空备份历史"""
        try:
            if os.path.exists(self.backup_history_file):
                os.remove(self.backup_history_file)
            return True
        except Exception as e:
            print(f"清空备份历史失败: {e}")
            return False
    
    def get_remote_directory_info(self, remote_path: str) -> Tuple[bool, List[Dict]]:
        """获取远程目录信息"""
        if not self.ssh_client.connected:
            return False, []
        
        return self.ssh_client.list_remote_directory(remote_path)
    
    def create_backup_schedule(self, schedule_config: Dict) -> bool:
        """创建备份计划（预留功能）"""
        # 这里可以实现定时备份功能
        # 暂时只是保存配置
        try:
            schedules_file = "backup_schedules.json"
            schedules = []
            
            if os.path.exists(schedules_file):
                with open(schedules_file, 'r', encoding='utf-8') as f:
                    schedules = json.load(f)
            
            schedules.append(schedule_config)
            
            with open(schedules_file, 'w', encoding='utf-8') as f:
                json.dump(schedules, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"创建备份计划失败: {e}")
            return False
    
    def validate_paths(self, local_path: str, remote_path: str, operation: str) -> Tuple[bool, str]:
        """验证路径有效性"""
        if operation == "download":
            # 验证本地路径的父目录是否存在
            local_parent = os.path.dirname(local_path)
            if local_parent and not os.path.exists(local_parent):
                return False, f"本地父目录不存在: {local_parent}"
            
            # 验证远程路径是否存在
            if self.ssh_client.connected:
                try:
                    self.ssh_client.sftp.stat(remote_path)
                except FileNotFoundError:
                    return False, f"远程路径不存在: {remote_path}"
                except Exception as e:
                    return False, f"无法访问远程路径: {str(e)}"
        
        elif operation == "upload":
            # 验证本地路径是否存在
            if not os.path.exists(local_path):
                return False, f"本地路径不存在: {local_path}"
            
            # 远程路径会在上传时自动创建
        
        return True, "路径验证通过"
    
    def get_connection_status(self) -> Dict:
        """获取连接状态信息"""
        if not self.current_session:
            return {'connected': False}
        
        return {
            'connected': self.ssh_client.connected,
            'server_name': self.current_session.get('server_name'),
            'connected_at': self.current_session.get('connected_at'),
            'server_host': self.current_session.get('server_config', {}).get('host')
        }
    
    def cancel_current_operation(self):
        """取消当前操作"""
        self.ssh_client.cancel_current_transfer()
