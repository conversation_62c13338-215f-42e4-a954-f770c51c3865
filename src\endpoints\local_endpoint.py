"""
本地端点
管理本地文件系统操作
"""
import os
import shutil
import stat
import time
from typing import List, Dict, Tuple
from .base_endpoint import BaseEndpoint


class LocalEndpoint(BaseEndpoint):
    """本地文件系统端点"""
    
    def __init__(self):
        super().__init__("local")
        self.connected = True  # 本地端点始终连接
    
    def connect(self) -> <PERSON><PERSON>[bool, str]:
        """连接到本地文件系统（始终成功）"""
        self.connected = True
        return True, "本地文件系统已就绪"
    
    def disconnect(self):
        """断开连接（本地端点无需断开）"""
        pass
    
    def list_directory(self, path: str) -> Tuple[bool, List[Dict]]:
        """列出本地目录内容"""
        try:
            if not os.path.exists(path):
                return False, f"路径不存在: {path}"
            
            if not os.path.isdir(path):
                return False, f"不是目录: {path}"
            
            files = []
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                try:
                    stat_info = os.stat(item_path)
                    file_info = {
                        'name': item,
                        'size': stat_info.st_size,
                        'is_dir': os.path.isdir(item_path),
                        'modified': stat_info.st_mtime,
                        'permissions': oct(stat_info.st_mode)[-3:]
                    }
                    files.append(file_info)
                except OSError:
                    # 跳过无法访问的文件
                    continue
            
            return True, files
            
        except Exception as e:
            return False, f"列出目录失败: {str(e)}"
    
    def path_exists(self, path: str) -> bool:
        """检查本地路径是否存在"""
        return os.path.exists(path)
    
    def is_directory(self, path: str) -> bool:
        """检查是否为目录"""
        return os.path.isdir(path)
    
    def get_path_info(self, path: str) -> Dict:
        """获取本地路径信息"""
        try:
            if not os.path.exists(path):
                return {}
            
            stat_info = os.stat(path)
            return {
                'size': stat_info.st_size,
                'modified': stat_info.st_mtime,
                'created': stat_info.st_ctime,
                'is_dir': os.path.isdir(path),
                'is_file': os.path.isfile(path),
                'permissions': oct(stat_info.st_mode)[-3:],
                'owner_readable': bool(stat_info.st_mode & stat.S_IRUSR),
                'owner_writable': bool(stat_info.st_mode & stat.S_IWUSR),
                'owner_executable': bool(stat_info.st_mode & stat.S_IXUSR)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def create_directory(self, path: str) -> bool:
        """创建本地目录"""
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception as e:
            print(f"创建目录失败: {e}")
            return False
    
    def remove_path(self, path: str) -> bool:
        """删除本地文件或目录"""
        try:
            if os.path.isfile(path):
                os.remove(path)
            elif os.path.isdir(path):
                shutil.rmtree(path)
            return True
        except Exception as e:
            print(f"删除路径失败: {e}")
            return False
    
    def copy_file(self, src: str, dst: str) -> bool:
        """复制本地文件"""
        try:
            # 确保目标目录存在
            dst_dir = os.path.dirname(dst)
            if dst_dir and not os.path.exists(dst_dir):
                os.makedirs(dst_dir)
            
            shutil.copy2(src, dst)
            return True
        except Exception as e:
            print(f"复制文件失败: {e}")
            return False
    
    def copy_directory(self, src: str, dst: str) -> bool:
        """复制本地目录"""
        try:
            if os.path.exists(dst):
                shutil.rmtree(dst)
            shutil.copytree(src, dst)
            return True
        except Exception as e:
            print(f"复制目录失败: {e}")
            return False
    
    def get_free_space(self, path: str) -> int:
        """获取路径所在磁盘的可用空间（字节）"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(path),
                    ctypes.pointer(free_bytes),
                    None,
                    None
                )
                return free_bytes.value
            else:  # Unix/Linux
                statvfs = os.statvfs(path)
                return statvfs.f_frsize * statvfs.f_bavail
        except:
            return 0
    
    def get_directory_size(self, path: str) -> int:
        """获取目录总大小"""
        total_size = 0
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except OSError:
                        # 跳过无法访问的文件
                        continue
        except Exception:
            pass
        return total_size
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        return "本地计算机"
    
    def get_home_directory(self) -> str:
        """获取用户主目录"""
        return os.path.expanduser("~")
    
    def get_current_directory(self) -> str:
        """获取当前工作目录"""
        return os.getcwd()
    
    def normalize_path(self, path: str) -> str:
        """标准化路径"""
        return os.path.normpath(os.path.expanduser(path))
    
    def join_path(self, *parts) -> str:
        """连接路径"""
        return os.path.join(*parts)
    
    def get_parent_directory(self, path: str) -> str:
        """获取父目录"""
        return os.path.dirname(path)
    
    def get_basename(self, path: str) -> str:
        """获取路径的基本名称"""
        return os.path.basename(path)
