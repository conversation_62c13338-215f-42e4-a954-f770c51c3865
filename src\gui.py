"""
远程文件备份工具图形界面
基于Tkinter实现
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
try:
    from .config_manager import ConfigManager
    from .ssh_client import SSHClient
except ImportError:
    from config_manager import ConfigManager
    from ssh_client import SSHClient


class BackupToolGUI:
    """备份工具图形界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("远程文件备份工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.ssh_client = SSHClient()
        
        # 界面变量
        self.selected_server = tk.StringVar()
        self.local_path = tk.StringVar()
        self.remote_path = tk.StringVar()
        self.transfer_mode = tk.StringVar(value="download")  # download 或 upload
        
        # 创建界面
        self.create_widgets()
        self.refresh_server_list()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 服务器配置区域
        self.create_server_section(main_frame, 0)
        
        # 传输配置区域
        self.create_transfer_section(main_frame, 1)
        
        # 进度显示区域
        self.create_progress_section(main_frame, 2)
        
        # 日志显示区域
        self.create_log_section(main_frame, 3)
        
        # 控制按钮区域
        self.create_control_section(main_frame, 4)
    
    def create_server_section(self, parent, row):
        """创建服务器配置区域"""
        # 服务器配置标题
        server_frame = ttk.LabelFrame(parent, text="服务器配置", padding="5")
        server_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        server_frame.columnconfigure(1, weight=1)
        
        # 服务器选择
        ttk.Label(server_frame, text="选择服务器:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.server_combo = ttk.Combobox(server_frame, textvariable=self.selected_server, state="readonly")
        self.server_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 服务器管理按钮
        ttk.Button(server_frame, text="添加服务器", command=self.add_server).grid(row=0, column=2, padx=(5, 0))
        ttk.Button(server_frame, text="编辑服务器", command=self.edit_server).grid(row=0, column=3, padx=(5, 0))
        ttk.Button(server_frame, text="删除服务器", command=self.delete_server).grid(row=0, column=4, padx=(5, 0))
        
        # 连接状态
        self.connection_label = ttk.Label(server_frame, text="未连接", foreground="red")
        self.connection_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        ttk.Button(server_frame, text="测试连接", command=self.test_connection).grid(row=1, column=2, pady=(5, 0))
    
    def create_transfer_section(self, parent, row):
        """创建传输配置区域"""
        transfer_frame = ttk.LabelFrame(parent, text="传输配置", padding="5")
        transfer_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        transfer_frame.columnconfigure(1, weight=1)
        
        # 传输模式选择
        mode_frame = ttk.Frame(transfer_frame)
        mode_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(mode_frame, text="传输模式:").pack(side=tk.LEFT)
        ttk.Radiobutton(mode_frame, text="下载 (远程→本地)", variable=self.transfer_mode, 
                       value="download").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(mode_frame, text="上传 (本地→远程)", variable=self.transfer_mode, 
                       value="upload").pack(side=tk.LEFT, padx=(10, 0))
        
        # 本地路径
        ttk.Label(transfer_frame, text="本地路径:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(transfer_frame, textvariable=self.local_path).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(transfer_frame, text="浏览", command=self.browse_local_path).grid(row=1, column=2)
        
        # 远程路径
        ttk.Label(transfer_frame, text="远程路径:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        ttk.Entry(transfer_frame, textvariable=self.remote_path).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 5), pady=(5, 0))
        ttk.Button(transfer_frame, text="浏览", command=self.browse_remote_path).grid(row=2, column=2, pady=(5, 0))
    
    def create_progress_section(self, parent, row):
        """创建进度显示区域"""
        progress_frame = ttk.LabelFrame(parent, text="传输进度", padding="5")
        progress_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 进度文本
        self.progress_label = ttk.Label(progress_frame, text="就绪")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
    
    def create_log_section(self, parent, row):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
    
    def create_control_section(self, parent, row):
        """创建控制按钮区域"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        # 控制按钮
        self.start_button = ttk.Button(control_frame, text="开始传输", command=self.start_transfer)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.cancel_button = ttk.Button(control_frame, text="取消传输", command=self.cancel_transfer, state="disabled")
        self.cancel_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def refresh_server_list(self):
        """刷新服务器列表"""
        servers = self.config_manager.list_servers()
        self.server_combo['values'] = servers
        if servers and not self.selected_server.get():
            self.selected_server.set(servers[0])
    
    def add_server(self):
        """添加服务器"""
        dialog = ServerConfigDialog(self.root, "添加服务器")
        if dialog.result:
            name, host, port, username, password = dialog.result
            if self.config_manager.add_server(name, host, port, username, password):
                self.log_message(f"服务器 '{name}' 添加成功")
                self.refresh_server_list()
                self.selected_server.set(name)
            else:
                messagebox.showerror("错误", "服务器名称已存在或添加失败")
    
    def edit_server(self):
        """编辑服务器"""
        server_name = self.selected_server.get()
        if not server_name:
            messagebox.showwarning("警告", "请先选择一个服务器")
            return
        
        server_config = self.config_manager.get_server(server_name)
        if not server_config:
            messagebox.showerror("错误", "获取服务器配置失败")
            return
        
        dialog = ServerConfigDialog(self.root, "编辑服务器", server_config)
        if dialog.result:
            name, host, port, username, password = dialog.result
            if name != server_name:
                # 服务器名称改变，需要删除旧的并添加新的
                self.config_manager.remove_server(server_name)
                self.config_manager.add_server(name, host, port, username, password)
            else:
                self.config_manager.update_server(name, host, port, username, password)
            
            self.log_message(f"服务器 '{name}' 更新成功")
            self.refresh_server_list()
            self.selected_server.set(name)
    
    def delete_server(self):
        """删除服务器"""
        server_name = self.selected_server.get()
        if not server_name:
            messagebox.showwarning("警告", "请先选择一个服务器")
            return
        
        if messagebox.askyesno("确认", f"确定要删除服务器 '{server_name}' 吗？"):
            if self.config_manager.remove_server(server_name):
                self.log_message(f"服务器 '{server_name}' 删除成功")
                self.refresh_server_list()
                self.connection_label.config(text="未连接", foreground="red")
            else:
                messagebox.showerror("错误", "删除服务器失败")
    
    def test_connection(self):
        """测试连接"""
        server_name = self.selected_server.get()
        if not server_name:
            messagebox.showwarning("警告", "请先选择一个服务器")
            return
        
        server_config = self.config_manager.get_server(server_name)
        if not server_config:
            messagebox.showerror("错误", "获取服务器配置失败")
            return
        
        self.log_message(f"正在连接到服务器 '{server_name}'...")
        
        def connect_thread():
            success, message = self.ssh_client.connect(
                server_config['host'],
                server_config['port'],
                server_config['username'],
                server_config['password']
            )
            
            self.root.after(0, lambda: self.on_connection_result(success, message))
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def on_connection_result(self, success, message):
        """连接结果回调"""
        if success:
            self.connection_label.config(text="已连接", foreground="green")
            self.log_message("连接成功")
        else:
            self.connection_label.config(text="连接失败", foreground="red")
            self.log_message(f"连接失败: {message}")
    
    def browse_local_path(self):
        """浏览本地路径"""
        path = filedialog.askdirectory(title="选择本地目录")
        if path:
            self.local_path.set(path)
    
    def browse_remote_path(self):
        """浏览远程路径"""
        if not self.ssh_client.connected:
            messagebox.showwarning("警告", "请先连接到服务器")
            return

        # 使用简单的输入对话框
        from tkinter.simpledialog import askstring
        current_path = self.remote_path.get() or "/"
        path = askstring("远程路径", "请输入远程目录路径:", initialvalue=current_path)
        if path:
            self.remote_path.set(path)
    
    def start_transfer(self):
        """开始传输"""
        # 验证输入
        if not self.selected_server.get():
            messagebox.showwarning("警告", "请选择服务器")
            return
        
        if not self.local_path.get() or not self.remote_path.get():
            messagebox.showwarning("警告", "请设置本地路径和远程路径")
            return
        
        # 检查连接
        if not self.ssh_client.connected:
            self.test_connection()
            if not self.ssh_client.connected:
                messagebox.showerror("错误", "请先连接到服务器")
                return
        
        # 禁用开始按钮，启用取消按钮
        self.start_button.config(state="disabled")
        self.cancel_button.config(state="normal")
        
        # 重置进度
        self.progress_var.set(0)
        self.progress_label.config(text="准备传输...")
        
        # 开始传输线程
        def transfer_thread():
            try:
                mode = self.transfer_mode.get()
                local_path = self.local_path.get()
                remote_path = self.remote_path.get()
                
                def progress_callback(current, total):
                    progress = (current / total) * 100 if total > 0 else 0
                    self.root.after(0, lambda: self.update_progress(progress, current, total))
                
                if mode == "download":
                    self.root.after(0, lambda: self.log_message(f"开始下载: {remote_path} -> {local_path}"))
                    success, message = self.ssh_client.download_directory(remote_path, local_path, progress_callback)
                else:
                    self.root.after(0, lambda: self.log_message(f"开始上传: {local_path} -> {remote_path}"))
                    success, message = self.ssh_client.upload_directory(local_path, remote_path, progress_callback)
                
                self.root.after(0, lambda: self.on_transfer_complete(success, message))
                
            except Exception as e:
                self.root.after(0, lambda: self.on_transfer_complete(False, f"传输异常: {str(e)}"))
        
        threading.Thread(target=transfer_thread, daemon=True).start()
    
    def update_progress(self, progress, current, total):
        """更新进度"""
        self.progress_var.set(progress)
        self.progress_label.config(text=f"进度: {progress:.1f}% ({current}/{total})")
    
    def on_transfer_complete(self, success, message):
        """传输完成回调"""
        self.start_button.config(state="normal")
        self.cancel_button.config(state="disabled")
        
        if success:
            self.progress_var.set(100)
            self.progress_label.config(text="传输完成")
            self.log_message(f"传输成功: {message}")
            messagebox.showinfo("成功", "传输完成")
        else:
            self.progress_label.config(text="传输失败")
            self.log_message(f"传输失败: {message}")
            messagebox.showerror("失败", f"传输失败: {message}")
    
    def cancel_transfer(self):
        """取消传输"""
        self.ssh_client.cancel_current_transfer()
        self.log_message("正在取消传输...")
    
    def on_closing(self):
        """关闭程序"""
        if self.ssh_client.connected:
            self.ssh_client.disconnect()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()


class ServerConfigDialog:
    """服务器配置对话框"""
    
    def __init__(self, parent, title, config=None):
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # 创建变量
        self.name_var = tk.StringVar(value=config.get('name', '') if config else '')
        self.host_var = tk.StringVar(value=config.get('host', '') if config else '')
        self.port_var = tk.StringVar(value=str(config.get('port', 22)) if config else '22')
        self.username_var = tk.StringVar(value=config.get('username', '') if config else '')
        self.password_var = tk.StringVar(value=config.get('password', '') if config else '')
        
        self.create_dialog_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def create_dialog_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 输入字段
        fields = [
            ("服务器名称:", self.name_var),
            ("主机地址:", self.host_var),
            ("端口:", self.port_var),
            ("用户名:", self.username_var),
            ("密码:", self.password_var)
        ]
        
        for i, (label, var) in enumerate(fields):
            ttk.Label(main_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=5)
            if label == "密码:":
                entry = ttk.Entry(main_frame, textvariable=var, show="*", width=30)
            else:
                entry = ttk.Entry(main_frame, textvariable=var, width=30)
            entry.grid(row=i, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT)
    
    def ok_clicked(self):
        """确定按钮点击"""
        # 验证输入
        if not all([self.name_var.get(), self.host_var.get(), self.username_var.get(), self.password_var.get()]):
            messagebox.showerror("错误", "请填写所有必填字段")
            return
        
        try:
            port = int(self.port_var.get())
            if port <= 0 or port > 65535:
                raise ValueError()
        except ValueError:
            messagebox.showerror("错误", "端口必须是1-65535之间的数字")
            return
        
        self.result = (
            self.name_var.get(),
            self.host_var.get(),
            port,
            self.username_var.get(),
            self.password_var.get()
        )
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()
