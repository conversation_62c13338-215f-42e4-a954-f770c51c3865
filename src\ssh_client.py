"""
SSH文件传输客户端
基于paramiko实现SSH连接和SFTP文件传输
"""
import os
import stat
import paramiko
from typing import Callable, Optional, Tuple
import threading
import time


class SSHClient:
    """SSH客户端类"""
    
    def __init__(self):
        self.ssh = None
        self.sftp = None
        self.connected = False
        self.cancel_transfer = False
    
    def connect(self, host: str, port: int, username: str, password: str, timeout: int = 30) -> Tuple[bool, str]:
        """连接到SSH服务器"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            self.ssh.connect(
                hostname=host,
                port=port,
                username=username,
                password=password,
                timeout=timeout
            )
            
            self.sftp = self.ssh.open_sftp()
            self.connected = True
            return True, "连接成功"
            
        except paramiko.AuthenticationException:
            return False, "认证失败：用户名或密码错误"
        except paramiko.SSHException as e:
            return False, f"SSH连接错误：{str(e)}"
        except Exception as e:
            return False, f"连接失败：{str(e)}"
    
    def disconnect(self):
        """断开SSH连接"""
        try:
            if self.sftp:
                self.sftp.close()
                self.sftp = None
            if self.ssh:
                self.ssh.close()
                self.ssh = None
            self.connected = False
        except Exception as e:
            print(f"断开连接时出错：{e}")
    
    def test_connection(self) -> bool:
        """测试连接是否有效"""
        if not self.connected or not self.ssh:
            return False
        try:
            self.ssh.exec_command('echo "test"', timeout=5)
            return True
        except:
            return False
    
    def list_remote_directory(self, remote_path: str) -> Tuple[bool, list]:
        """列出远程目录内容"""
        if not self.connected:
            return False, []
        
        try:
            files = []
            for item in self.sftp.listdir_attr(remote_path):
                file_info = {
                    'name': item.filename,
                    'size': item.st_size,
                    'is_dir': stat.S_ISDIR(item.st_mode),
                    'modified': item.st_mtime
                }
                files.append(file_info)
            return True, files
        except Exception as e:
            return False, f"列出目录失败：{str(e)}"
    
    def create_remote_directory(self, remote_path: str) -> bool:
        """创建远程目录"""
        if not self.connected:
            return False
        
        try:
            self.sftp.mkdir(remote_path)
            return True
        except Exception as e:
            print(f"创建远程目录失败：{e}")
            return False
    
    def download_file(self, remote_path: str, local_path: str, 
                     progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """下载单个文件"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            # 确保本地目录存在
            local_dir = os.path.dirname(local_path)
            if local_dir and not os.path.exists(local_dir):
                os.makedirs(local_dir)
            
            # 获取文件大小
            file_size = self.sftp.stat(remote_path).st_size
            
            def progress_wrapper(transferred, total):
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                if progress_callback:
                    progress_callback(transferred, total)
            
            self.sftp.get(remote_path, local_path, callback=progress_wrapper)
            return True, "下载成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"下载失败：{str(e)}"
    
    def upload_file(self, local_path: str, remote_path: str, 
                   progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """上传单个文件"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            # 确保远程目录存在
            remote_dir = os.path.dirname(remote_path).replace('\\', '/')
            if remote_dir:
                try:
                    self.sftp.stat(remote_dir)
                except FileNotFoundError:
                    self._create_remote_path(remote_dir)
            
            def progress_wrapper(transferred, total):
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                if progress_callback:
                    progress_callback(transferred, total)
            
            self.sftp.put(local_path, remote_path, callback=progress_wrapper)
            return True, "上传成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"上传失败：{str(e)}"
    
    def _create_remote_path(self, remote_path: str):
        """递归创建远程路径"""
        parts = remote_path.split('/')
        current_path = ""
        
        for part in parts:
            if not part:
                continue
            current_path += "/" + part if current_path else part
            try:
                self.sftp.stat(current_path)
            except FileNotFoundError:
                self.sftp.mkdir(current_path)
    
    def download_directory(self, remote_dir: str, local_dir: str, 
                          progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """下载整个目录"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            self.cancel_transfer = False
            total_files = self._count_remote_files(remote_dir)
            processed_files = 0
            
            def download_recursive(remote_path: str, local_path: str):
                nonlocal processed_files
                
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                
                # 确保本地目录存在
                if not os.path.exists(local_path):
                    os.makedirs(local_path)
                
                # 列出远程目录内容
                for item in self.sftp.listdir_attr(remote_path):
                    if self.cancel_transfer:
                        raise Exception("传输已取消")
                    
                    remote_item_path = f"{remote_path}/{item.filename}"
                    local_item_path = os.path.join(local_path, item.filename)
                    
                    if stat.S_ISDIR(item.st_mode):
                        # 递归下载子目录
                        download_recursive(remote_item_path, local_item_path)
                    else:
                        # 下载文件
                        def file_progress(transferred, total):
                            if progress_callback:
                                overall_progress = (processed_files + transferred / total) / total_files
                                progress_callback(overall_progress * 100, 100)
                        
                        success, msg = self.download_file(remote_item_path, local_item_path, file_progress)
                        if not success:
                            raise Exception(f"下载文件失败：{msg}")
                        
                        processed_files += 1
                        if progress_callback:
                            progress_callback(processed_files / total_files * 100, 100)
            
            download_recursive(remote_dir, local_dir)
            return True, "目录下载成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"目录下载失败：{str(e)}"
    
    def upload_directory(self, local_dir: str, remote_dir: str, 
                        progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """上传整个目录"""
        if not self.connected:
            return False, "未连接到服务器"
        
        try:
            self.cancel_transfer = False
            total_files = self._count_local_files(local_dir)
            processed_files = 0
            
            def upload_recursive(local_path: str, remote_path: str):
                nonlocal processed_files
                
                if self.cancel_transfer:
                    raise Exception("传输已取消")
                
                # 确保远程目录存在
                try:
                    self.sftp.stat(remote_path)
                except FileNotFoundError:
                    self.sftp.mkdir(remote_path)
                
                # 遍历本地目录
                for item in os.listdir(local_path):
                    if self.cancel_transfer:
                        raise Exception("传输已取消")
                    
                    local_item_path = os.path.join(local_path, item)
                    remote_item_path = f"{remote_path}/{item}"
                    
                    if os.path.isdir(local_item_path):
                        # 递归上传子目录
                        upload_recursive(local_item_path, remote_item_path)
                    else:
                        # 上传文件
                        def file_progress(transferred, total):
                            if progress_callback:
                                overall_progress = (processed_files + transferred / total) / total_files
                                progress_callback(overall_progress * 100, 100)
                        
                        success, msg = self.upload_file(local_item_path, remote_item_path, file_progress)
                        if not success:
                            raise Exception(f"上传文件失败：{msg}")
                        
                        processed_files += 1
                        if progress_callback:
                            progress_callback(processed_files / total_files * 100, 100)
            
            upload_recursive(local_dir, remote_dir)
            return True, "目录上传成功"
            
        except Exception as e:
            if "传输已取消" in str(e):
                return False, "传输已取消"
            return False, f"目录上传失败：{str(e)}"
    
    def _count_remote_files(self, remote_dir: str) -> int:
        """统计远程目录中的文件数量"""
        count = 0
        try:
            for item in self.sftp.listdir_attr(remote_dir):
                if stat.S_ISDIR(item.st_mode):
                    count += self._count_remote_files(f"{remote_dir}/{item.filename}")
                else:
                    count += 1
        except:
            pass
        return count
    
    def _count_local_files(self, local_dir: str) -> int:
        """统计本地目录中的文件数量"""
        count = 0
        try:
            for item in os.listdir(local_dir):
                item_path = os.path.join(local_dir, item)
                if os.path.isdir(item_path):
                    count += self._count_local_files(item_path)
                else:
                    count += 1
        except:
            pass
        return count
    
    def cancel_current_transfer(self):
        """取消当前传输"""
        self.cancel_transfer = True
